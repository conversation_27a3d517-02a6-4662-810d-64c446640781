package com.zenyte.game.world.entity.player.calog;

import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR>
 */
public enum CABossType {

	// Referenced enums with updated names and collLogVal values
	ABYSSAL_SIRE(12917, 1, false), //works
	ALCHEMICAL_HYDRA(12926, 2, false), //works
	ARAXXOR(14713, -1, false),
	BARROWS_CHESTS(12893, 3, false),
	BRYOPHYTA(12925, 4, false),
	CALLISTO_AND_ARTIO(12901, 5, false),
	CERBERUS(12916, 6, false),
	CHAOS_ELEMENTAL(14714, 7, false),
	CHAOS_FANATIC(12910, 10, false),
	COMMANDER_ZILYANA(12896, 11, false),
	CORPOREAL_BEAST(12908, 9, false),
	CRAZY_ARCHEOLOGIST(12912, 11, false),
	DAGANNOTH_KING_PRIME(12898, 39, false),
	DAGANNOTH_KING_REX(12899, 39, false),
	DAGANNOTH_KING_SUPREME(12900, 39, false),
	DUKE_SUCELLUS(14715, -1, false),
	THE_FIGHT_CAVES_TZTOK_JAD(12913, 14, false),
	THE_GAUNTLET_CRYSTALLINE_HUNLLEF(12931, 15, false),
	GENERAL_GRAARDOR(12895, 16, false),
	GIANT_MOLE(12906, 17, false),
	GROTESQUE_GUARDIANS(12923, 18, false),
	HESPORI(12927, 19, false),
	THE_INFERNO_TZKAL_ZUK(12921, 13, false),
	KALPHITE_QUEEN(12907, 21, false),
	KING_BLACK_DRAGON(12905, 22, false),
	KRAKEN(12914, 23, false),
	KREE_ARRA(12894, 24, false),
	KRIL_TSUTSAROTH(12897, 25, false),
	NEX(13171, 26, false),
	THE_NIGHTMARE(12934, 27, false),
	OBOR(12920, 28, false),
	PHANTOM_MUSPAH(14712, 29, false),
	RISE_OF_THE_SIX(14716, -1, false),
	SARACHNIS(12929, 31, false),
	SCORPIA(12911, 32, false),
	SKOTIZO(12918, 33, false),
	THERMONUCLEAR_SMOKE_DEVIL(12915, 34, false),
	TORMENTED_DEMONS(14717, -1, false),
	VANSTROM_KLAUSE(14718, -1, false),
	VARDORVIS(14719, 37, false),
	VENENATIS_AND_SPINDEL(12902, 38, false),
	VETION_AND_CALVARION(12903, 39, false),
	VORKATH(12924, 40, false),
	WINTERTODT(12919, 41, false),
	ZALCANO(12930, 42, false),
	ZULRAH(12909, 43, false),

	// Old/unreferenced enums moved to bottom
	COX(12891, 0, true),
	COX_CM(12892, 0, true),
	CORRUPTED_HUNLLEF(12932, -1, false),
	DERANGED_ARCHEOLOGIST(12922, 0, false),
	THE_MIMIC(12928, 0, false),
	PHOSANI_NIGHTMARE(13002, 26, false),
	TEMPOROSS(12936, 32, false),
	TOB_ENTRY_MODE(12939, 1, true),
	TOB(12935, 1, true),
	TOB_HARD_MODE(12938, 1, true),
	TOA_ENTRY_MODE(14297, 2, true),
	TOA(14298, 2, true),
	TOA_HARD_MODE(14299, 2, true),
	TZHAAR_KET_RAK_CHALLENGES(12937, 19, false),
	;

	public static final CABossType[] values = values();
	private final int taskVarBit;
	private final int collLogVal;
	private final boolean raid;

	CABossType(final int taskVarBit, int collLogVal, boolean raid) {
		this.taskVarBit = taskVarBit;
		this.collLogVal = collLogVal;
		this.raid = raid;
	}

	public int getTaskVarBit() {
		return taskVarBit;
	}

	public int getCollLogVal() {
		return collLogVal;
	}

	public int getCollLogVal(Player player) {
		if (player != null && player.isDebugging) {
			// Get caller information for debugging
			StackTraceElement caller = Thread.currentThread().getStackTrace()[2];
			String callerClass = caller.getClassName();
			String callerMethod = caller.getMethodName();
			int callerLine = caller.getLineNumber();

			player.sendMessage("DEBUG: getCollLogVal() called for boss type: " + this.name() +
				" | Returning value: " + collLogVal +
				" | Called from: " + callerClass + "." + callerMethod + "() line " + callerLine);
		}
		return collLogVal;
	}

	public boolean isRaid() { return raid; }
}
