package com.near_reality.cache.interfaces.teleports.categories

import com.near_reality.cache.interfaces.teleports.builder.TeleportsBuilder

internal fun TeleportsBuilder.wilderness() = "Wilderness Teleports"(10004) {
    "<col=ff0000>Bandit Camp"(-21166, 3038, 3651, 0, "")
    "<col=ff0000>Black Chinchompas"(-11959, 3144, 3770, 0, "")
    "<col=ff0000>Chaos Temple"(-21166, 3235, 3637, 0, "")
    "<col=ff0000>Demonic Ruins"(-13501, 3288, 3886, 0, "")
    "<col=ff0000>Eastern Dragons"(-13510, 3346, 3666, 0, "")
    "<col=ff0000>Elder Chaos Druids"(-20517, 3235, 3637, 0, "")
    "Ferox Enclave"(-26651, 3150, 3636, 0, "")
    "<col=ff0000>Lava Maze"(-21166, 3027, 3839, 0, "")
    "Mage Bank"(-21791, 2539, 4716, 0, "")
    "<col=ff0000>Resource Area"(-11934, 3176, 3949, 0, "")
    "<col=ff0000>Revenants (17)"(-21813, 3079, 3653, 0, "")
    "<col=ff0000>Revenants (39)"(-22305, 3129, 3829, 0, "")
    "<col=ff0000>Rogues' Castle"(-1203, 3285, 3919, 0, "")
    "<col=ff0000>Varrock Multi"(-526, 3243, 3519, 0, "")
    "<col=ff0000>Western Dragons"(-13510, 2979, 3595, 0, "")
}