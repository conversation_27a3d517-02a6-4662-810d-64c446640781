package com.near_reality.cache.interfaces.teleports.categories

import com.near_reality.cache.interfaces.teleports.builder.TeleportsBuilder
import com.zenyte.game.item.ItemId

internal fun TeleportsBuilder.skilling() = "Skilling Teleports"(10003) {
    "Agility (1): Gnome Course"(-13655, 2472,3437,0,"")
    "Agility (10): <PERSON><PERSON><PERSON> Rooftop"(-ItemId.MARK_OF_GRACE, 3104,3278,0,"")
    "Agility (20): Al-Kharid Rooftop"(-ItemId.MARK_OF_GRACE, 3273,3196,0,"")
    "Agility (30): Varrock Rooftop"(-ItemId.MARK_OF_GRACE, 3222,3414,0,"")
    "Agility (35): Barbarian Outpost"(-1365, 2548, 3569, 0, "")
    "Agility (40): Canifis Rooftop"(-ItemId.MARK_OF_GRACE, 3505,3487,0,"")
    "Agility (50): Falador Rooftop"(-ItemId.MARK_OF_GRACE, 3036,3340,0,"")
    "<col=ff0000>Agility (52): <PERSON>y <PERSON>gility Course"(-553, 2998,3913,0,"")
    "Agility (60): Seers Rooftop"(-ItemId.MARK_OF_GRACE, 2729,3486,0,"")
    "Agility (70): Pollnivneach Rooftop"(-ItemId.MARK_OF_GRACE, 3351,2961,0,"")
    "Agility (75): Prif Agility Course"(-23962, 3252,6109,0,"")
    "Agility (80): Rellekka Rooftop"(-ItemId.MARK_OF_GRACE, 2626,3679,0,"")
    "Agility (90): Ardougne Rooftop"(-ItemId.MARK_OF_GRACE, 2672,3296,0,"")
    "Farming: Guild"(-13646, 1249, 3719, 0, "")
    "Farming: Harmony Island"(-10887, 3800, 2829, 0, "")
    "Farming: Falador Herb Patch"(-269, 3051, 3304, 0, "")
    "Farming: Port Phasmatys Herb Patch"(-253, 3603, 3533, 0, "")
    "Farming: Catherby Herb Patch"(-261, 2803, 3471, 0, "")
    "Farming: Ardougne Herb Patch"(-249, 2674, 3374, 0, "")
    "Farming: Hosidius Herb Patch"(-249, 1742, 3548, 0, "")
    "Firemaking: Wintertodt"(4266, 1630, 3946, 0, "https://oldschool.runescape.wiki/w/Wintertodt")
    "Fishing: Aerial Fishing"(-22817, 1368,3628,0,"")
    "Fishing: Guild"(-383, 2611, 3392, 0, "")
    "Fishing: Otto's Grotto"(-11323, 2499,3508,0,"")
    "Hunter: Feldip Hills"(-10088, 2525, 2916, 0, "")
    "Mining: Guild"(-1275, 3048, 9763, 0, "")
    "Mining: Quarry"(-13136, 3171,2911,0,"")
    "Mining: Motherlode Mine"(-12011, 3728,5690,0,"")
    "Runecrafting: Abyss"(-5509, 3016,4847,0,"")
    "Runecrafting: Dark Altar"(-13446, 1712,3882,0,"")
    "Runecrafting: Astral Altar"(-9075, 2149,3870,0,"")
    "Runecrafting: ZMI Altar"(-24951, 2467,3246,0,"")
    "Smithing: Blast Furnance"(-2357, 1939,4958,0,"")
    "Thieving: Rogues' Den"(-5553, 3057,4980,1,"")
    "Woodcutting: Guild"(-1359, 1604, 3499, 0, "")
}
