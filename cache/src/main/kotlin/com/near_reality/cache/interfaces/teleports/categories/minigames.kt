package com.near_reality.cache.interfaces.teleports.categories

import com.near_reality.cache.interfaces.teleports.builder.TeleportsBuilder

internal fun TeleportsBuilder.minigames() = "Minigames Teleports"(10007) {
    "Barrows"(4267, 3565, 3306, 0, "")
    "Blast Furnace"(-2357, 2931, 10196, 0, "")
    "Castle Wars"(-4037, 2442, 3089, 0, "")
    "Duel Arena"(-1215, 3367, 3267, 0, "")
    "Fight Caves"(5629, 2444, 5170, 0, "")
    "Motherlode Mine"(-12013, 3725, 5686, 0, "")
    "Party Room"(-1038, 3046, 3375, 0, "")
    "Pest Control"(-11665, 2658, 2672, 0, "")
    "Puro Puro"(-19732, 2417, 4445, 0, "")
    "Pyramid Plunder"(-26945, 3288, 2786, 0, "")
    "Tears of Guthix"(-4704, 3244, 9500, 2, "")
    "The Inferno"(5630, 2495, 5103, 0, "")
    "Warriors Guild"(-12954, 2880, 3546, 0, "")
    "Wintertodt"(4266, 1630, 3946, 0, "https://oldschool.runescape.wiki/w/Wintertodt")
    "Zalcano"(4273, 3033, 6068, 0, "https://oldschool.runescape.wiki/w/Zalcano")
}