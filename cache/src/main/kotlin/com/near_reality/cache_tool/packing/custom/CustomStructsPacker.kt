package com.near_reality.cache_tool.packing.custom

import mgi.types.config.StructDefinitions

object CustomStructsPacker {
    @JvmStatic
    fun pack() {
        StructDefinitions.get(500).copy(10500).apply {
            this.parameters[689] = "<PERSON> Sucellus"
            this.parameters[690] = 10500
            this.pack()
        }
        StructDefinitions.get(500).copy(10501).apply {
            this.parameters[689] = "Vardorvis"
            this.parameters[690] = 10501
            this.pack()
        }
        StructDefinitions.get(500).copy(10502).apply {
            this.parameters[689] = "Tormented Demons"
            this.parameters[690] = 10502
            this.pack()
        }
        StructDefinitions.get(500).copy(10503).apply {
            this.parameters[689] = "Araxxor"
            this.parameters[690] = 10503
            this.pack()
        }

        // Ensure the last 5 collection log structs have proper interface layout parameters
        // These are the structs that are missing the Combat achievements button
        fixCollectionLogStruct(501, "Vet'ion and Calvar'ion")
        fixCollectionLogStruct(502, "Vorkath")
        fixCollectionLogStruct(503, "Wintertodt")
        fixCollectionLogStruct(504, "Zalcano")
        fixCollectionLogStruct(505, "Zulrah")
    }

    private fun fixCollectionLogStruct(structId: Int, name: String) {
        // Copy from a working collection log struct (like 476 - Abyssal Sire) to ensure all interface parameters are present
        StructDefinitions.get(476).copy(structId).apply {
            // Keep the original name and enum reference
            this.parameters[689] = name
            // Keep the original enum ID if it exists, otherwise use the struct ID
            if (!this.parameters.containsKey(690)) {
                this.parameters[690] = structId
            }
            this.pack()
        }
    }
}