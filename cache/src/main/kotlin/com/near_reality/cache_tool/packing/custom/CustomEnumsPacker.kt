package com.near_reality.cache_tool.packing.custom

import com.zenyte.game.item.ItemId
import mgi.types.config.enums.EnumDefinitions
import net.runelite.cache.util.ScriptVarType

object CustomEnumsPacker {
    @JvmStatic
    fun pack() {
        /* Bosses Collection Log Entries */
        EnumDefinitions.get(2103).apply {
            values.clear()
            // Map collLogVal indices to correct struct IDs based on CABossType.java
            this.values[0] = 476    // ABYSSAL_SIRE (collLogVal = 0)
            this.values[1] = 539    // ALCHEMICAL_HYDRA (collLogVal = 1)
            this.values[3] = 477    // BARROWS_CHESTS (collLogVal = 3)
            this.values[4] = 478    // BRYOPHYTA (collLogVal = 4)
            this.values[5] = 479    // CALLISTO_AND_ARTIO (collLogVal = 5)
            this.values[6] = 480    // CERBERUS (collLogVal = 6)
            this.values[7] = 481    // CHAOS_ELEMENTAL (collLogVal = 7)
            this.values[9] = 483    // CORPOREAL_BEAST (collLogVal = 9)
            this.values[10] = 482   // CHAOS_FANATIC (collLogVal = 10)
            this.values[11] = 484   // COMMANDER_ZILYANA (collLogVal = 11) - also CRAZY_ARCHEOLOGIST
            this.values[13] = 486   // THE_INFERNO_TZKAL_ZUK (collLogVal = 13)
            this.values[14] = 10500 // THE_FIGHT_CAVES_TZTOK_JAD (collLogVal = 14)
            this.values[15] = 500   // THE_GAUNTLET_CRYSTALLINE_HUNLLEF (collLogVal = 15)
            this.values[16] = 487   // GENERAL_GRAARDOR (collLogVal = 16)
            this.values[17] = 488   // GIANT_MOLE (collLogVal = 17)
            this.values[18] = 489   // GROTESQUE_GUARDIANS (collLogVal = 18)
            this.values[19] = 541   // HESPORI (collLogVal = 19)
            this.values[21] = 499   // KALPHITE_QUEEN (collLogVal = 21)
            this.values[22] = 490   // KING_BLACK_DRAGON (collLogVal = 22)
            this.values[23] = 491   // KRAKEN (collLogVal = 23)
            this.values[24] = 492   // KREE_ARRA (collLogVal = 24)
            this.values[25] = 493   // KRIL_TSUTSAROTH (collLogVal = 25)
            this.values[26] = 3769  // NEX (collLogVal = 26)
            this.values[27] = 1263  // THE_NIGHTMARE (collLogVal = 27)
            this.values[28] = 601   // OBOR (collLogVal = 28)
            this.values[29] = 4455  // PHANTOM_MUSPAH (collLogVal = 29)
            this.values[31] = 496   // SARACHNIS (collLogVal = 31)
            this.values[32] = 497   // SCORPIA (collLogVal = 32)
            this.values[33] = 498   // SKOTIZO (collLogVal = 33)
            this.values[34] = 10502 // THERMONUCLEAR_SMOKE_DEVIL (collLogVal = 34)
            this.values[37] = 10322 // VARDORVIS (collLogVal = 37)
            this.values[38] = 10501 // VENENATIS_AND_SPINDEL (collLogVal = 38)
            this.values[39] = 501   // VETION_AND_CALVARION (collLogVal = 39) - also DAGANNOTH_KINGS
            this.values[40] = 502   // VORKATH (collLogVal = 40)
            this.values[41] = 503   // WINTERTODT (collLogVal = 41)
            this.values[42] = 504   // ZALCANO (collLogVal = 42)
            this.values[43] = 505   // ZULRAH (collLogVal = 43)
            this.pack()
        }

        EnumDefinitions.create(10500, ScriptVarType.INTEGER, ScriptVarType.NAMEDOBJ).apply {
            this.values[0] = ItemId.CHROMIUM_INGOT
            this.values[1] = ItemId.MAGUS_ICON
            this.values[2] = ItemId.EYE_OF_THE_DUKE
            this.values[3] = ItemId.VIRTUS_MASK
            this.values[4] = ItemId.VIRTUS_ROBE_TOP
            this.values[5] = ItemId.VIRTUS_ROBE_LEGS
            this.values[6] = ItemId.FROZEN_TABLET
            this.values[7] = ItemId.AWAKENERS_ORB
            this.values[8] = ItemId.ICE_QUARTZ
            this.pack()
        }

        EnumDefinitions.create(10501, ScriptVarType.INTEGER, ScriptVarType.NAMEDOBJ).apply {
            this.values[0] = ItemId.CHROMIUM_INGOT
            this.values[1] = ItemId.ULTOR_ICON
            this.values[2] = ItemId.EXECUTIONERS_AXE_HEAD
            this.values[3] = ItemId.VIRTUS_MASK
            this.values[4] = ItemId.VIRTUS_ROBE_TOP
            this.values[5] = ItemId.VIRTUS_ROBE_LEGS
            this.values[6] = ItemId.STRANGLED_TABLET
            this.values[7] = ItemId.AWAKENERS_ORB
            this.values[8] = ItemId.BLOOD_QUARTZ
            this.pack()
        }

        /* Tormented Demons */
        EnumDefinitions.create(10502, ScriptVarType.INTEGER, ScriptVarType.NAMEDOBJ).apply {
            this.values[0] = ItemId.TORMENTED_SYNAPSE
            this.values[1] = ItemId.BURNING_CLAW
            this.values[2] = ItemId.SMOULDERING_GLAND
            this.values[3] = ItemId.SMOULDERING_PILE_OF_FLESH
            this.values[4] = ItemId.SMOULDERING_HEART
            this.pack()
        }

        /* Arraxor */
        EnumDefinitions.create(10503, ScriptVarType.INTEGER, ScriptVarType.NAMEDOBJ).apply {
            this.values[0] = ItemId.NOXIOUS_BLADE
            this.values[1] = ItemId.NOXIOUS_POINT
            this.values[2] = ItemId.NOXIOUS_POMMEL
            this.values[3] = ItemId.ARAXYTE_FANG
            this.values[4] = ItemId.ARAXYTE_HEAD
            this.values[5] = ItemId.JAR_OF_VENOM
            this.values[6] = ItemId.NID
            this.pack()
        }

        EnumDefinitions.create(20002, ScriptVarType.INTEGER, ScriptVarType.STRING).apply {
            defaultString = "Overview"
            this.values[0] = "Overview"
            this.values[1] = "Melee"
            this.values[2] = "Ranged"
            this.values[3] = "Magic"
            this.values[4] = "Supplies"
            this.values[5] = "Skilling"
            this.values[6] = "Jewelry"
            this.values[7] = "General"
            this.values[8] = "Slayer"
            this.values[9] = "Bounty Hunter"
            this.values[10] = "Capes"
            this.values[11] = "Blood Money"
            this.values[12] = "Loyalty"
            this.values[13] = "Vote"
            this.pack()
        }
    }
}