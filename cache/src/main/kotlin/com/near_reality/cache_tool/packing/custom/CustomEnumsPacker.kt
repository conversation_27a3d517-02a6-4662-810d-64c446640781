package com.near_reality.cache_tool.packing.custom

import com.zenyte.game.item.ItemId
import mgi.types.config.enums.EnumDefinitions
import net.runelite.cache.util.ScriptVarType

object CustomEnumsPacker {
    @JvmStatic
    fun pack() {
        /* Bosses Collection Log Entries */
        EnumDefinitions.get(2103).apply {
            values.clear()
            // Sequential enum indices for collection log interface
            var idx = 0
            this.values[idx++] = 476    // 0: ABYSSAL_SIRE
            this.values[idx++] = 539    // 1: ALCHEMICAL_HYDRA
            this.values[idx++] = 10503  // 2: ARAXXOR
            this.values[idx++] = 477    // 3: BARROWS_CHESTS
            this.values[idx++] = 478    // 4: BRYOPHYTA
            this.values[idx++] = 479    // 5: CALLISTO_AND_ARTIO
            this.values[idx++] = 480    // 6: CERBERUS
            this.values[idx++] = 481    // 7: CHAOS_ELEMENTAL
            this.values[idx++] = 482    // 8: CHAOS_FANATIC
            this.values[idx++] = 483    // 9: CORPOREAL_BEAST
            this.values[idx++] = 484    // 10: COMMANDER_ZILYANA
            this.values[idx++] = 485    // 11: CRAZY_ARCHEOLOGIST
            this.values[idx++] = 501    // 12: DAGANNOTH_KINGS
            this.values[idx++] = 10321  // 13: DUKE_SUCELLUS
            this.values[idx++] = 486    // 14: THE_INFERNO_TZKAL_ZUK
            this.values[idx++] = 10500  // 15: THE_FIGHT_CAVES_TZTOK_JAD
            this.values[idx++] = 500    // 16: THE_GAUNTLET_CRYSTALLINE_HUNLLEF
            this.values[idx++] = 487    // 17: GENERAL_GRAARDOR
            this.values[idx++] = 488    // 18: GIANT_MOLE
            this.values[idx++] = 489    // 19: GROTESQUE_GUARDIANS
            this.values[idx++] = 541    // 20: HESPORI
            this.values[idx++] = 499    // 21: KALPHITE_QUEEN
            this.values[idx++] = 490    // 22: KING_BLACK_DRAGON
            this.values[idx++] = 491    // 23: KRAKEN
            this.values[idx++] = 492    // 24: KREE_ARRA
            this.values[idx++] = 493    // 25: KRIL_TSUTSAROTH
            this.values[idx++] = 3769   // 26: NEX
            this.values[idx++] = 1263   // 27: THE_NIGHTMARE
            this.values[idx++] = 601    // 28: OBOR
            this.values[idx++] = 4455   // 29: PHANTOM_MUSPAH
            this.values[idx++] = 495    // 30: RISE_OF_THE_SIX
            this.values[idx++] = 496    // 31: SARACHNIS
            this.values[idx++] = 497    // 32: SCORPIA
            this.values[idx++] = 498    // 33: SKOTIZO
            this.values[idx++] = 10502  // 34: THERMONUCLEAR_SMOKE_DEVIL
            this.values[idx++] = 10323  // 35: TORMENTED_DEMONS
            this.values[idx++] = 10324  // 36: VANSTROM_KLAUSE
            this.values[idx++] = 10322  // 37: VARDORVIS
            this.values[idx++] = 10501  // 38: VENENATIS_AND_SPINDEL
            this.values[idx++] = 10325  // 39: VETION_AND_CALVARION
            this.values[idx++] = 502    // 40: VORKATH
            this.values[idx++] = 503    // 41: WINTERTODT
            this.values[idx++] = 504    // 42: ZALCANO
            this.values[idx++] = 505    // 43: ZULRAH
            this.pack()
        }

        EnumDefinitions.create(10500, ScriptVarType.INTEGER, ScriptVarType.NAMEDOBJ).apply {
            this.values[0] = ItemId.CHROMIUM_INGOT
            this.values[1] = ItemId.MAGUS_ICON
            this.values[2] = ItemId.EYE_OF_THE_DUKE
            this.values[3] = ItemId.VIRTUS_MASK
            this.values[4] = ItemId.VIRTUS_ROBE_TOP
            this.values[5] = ItemId.VIRTUS_ROBE_LEGS
            this.values[6] = ItemId.FROZEN_TABLET
            this.values[7] = ItemId.AWAKENERS_ORB
            this.values[8] = ItemId.ICE_QUARTZ
            this.pack()
        }

        EnumDefinitions.create(10501, ScriptVarType.INTEGER, ScriptVarType.NAMEDOBJ).apply {
            this.values[0] = ItemId.CHROMIUM_INGOT
            this.values[1] = ItemId.ULTOR_ICON
            this.values[2] = ItemId.EXECUTIONERS_AXE_HEAD
            this.values[3] = ItemId.VIRTUS_MASK
            this.values[4] = ItemId.VIRTUS_ROBE_TOP
            this.values[5] = ItemId.VIRTUS_ROBE_LEGS
            this.values[6] = ItemId.STRANGLED_TABLET
            this.values[7] = ItemId.AWAKENERS_ORB
            this.values[8] = ItemId.BLOOD_QUARTZ
            this.pack()
        }

        /* Tormented Demons */
        EnumDefinitions.create(10502, ScriptVarType.INTEGER, ScriptVarType.NAMEDOBJ).apply {
            this.values[0] = ItemId.TORMENTED_SYNAPSE
            this.values[1] = ItemId.BURNING_CLAW
            this.values[2] = ItemId.SMOULDERING_GLAND
            this.values[3] = ItemId.SMOULDERING_PILE_OF_FLESH
            this.values[4] = ItemId.SMOULDERING_HEART
            this.pack()
        }

        /* Arraxor */
        EnumDefinitions.create(10503, ScriptVarType.INTEGER, ScriptVarType.NAMEDOBJ).apply {
            this.values[0] = ItemId.NOXIOUS_BLADE
            this.values[1] = ItemId.NOXIOUS_POINT
            this.values[2] = ItemId.NOXIOUS_POMMEL
            this.values[3] = ItemId.ARAXYTE_FANG
            this.values[4] = ItemId.ARAXYTE_HEAD
            this.values[5] = ItemId.JAR_OF_VENOM
            this.values[6] = ItemId.NID
            this.pack()
        }

        EnumDefinitions.create(20002, ScriptVarType.INTEGER, ScriptVarType.STRING).apply {
            defaultString = "Overview"
            this.values[0] = "Overview"
            this.values[1] = "Melee"
            this.values[2] = "Ranged"
            this.values[3] = "Magic"
            this.values[4] = "Supplies"
            this.values[5] = "Skilling"
            this.values[6] = "Jewelry"
            this.values[7] = "General"
            this.values[8] = "Slayer"
            this.values[9] = "Bounty Hunter"
            this.values[10] = "Capes"
            this.values[11] = "Blood Money"
            this.values[12] = "Loyalty"
            this.values[13] = "Vote"
            this.pack()
        }
    }
}