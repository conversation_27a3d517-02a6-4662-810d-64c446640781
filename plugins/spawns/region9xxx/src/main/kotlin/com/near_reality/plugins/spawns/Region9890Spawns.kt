package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9890Spawns : NPCSpawnsScript() {
    init {
        BASILISK_KNIGHT(2432, 10392, 0, SOUTH, 5)
        DAGANNOTH_7260(2433, 10417, 0, SOUTH, 2)
        BASILISK_KNIGHT(2434, 10387, 0, SOUTH, 5)
        DAGANNOTH_7260(2435, 10414, 0, SOUTH, 2)
        DAGANNOTH_7260(2446, 10430, 0, SOUTH, 2)
        BASILISK_KNIGHT(2450, 10380, 0, SOUTH, 5)
        BASILISK_KNIGHT(2450, 10389, 0, <PERSON>OUT<PERSON>, 5)
        BASILISK_KNIGHT(2453, 10384, 0, SOUTH, 5)
        BASILISK_KNIGHT(2455, 10378, 0, <PERSON><PERSON><PERSON>H, 5)
        BASILISK_KNIGHT(2455, 10390, 0, SOUTH, 5)
        BASILISK_KNIGHT(2459, 10382, 0, SOUTH, 5)
        BASILISK_KNIGHT(2460, 10388, 0, SOUTH, 5)
        BASILISK_417(2475, 10403, 0, SOUTH, 6)
        BASILISK_417(2478, 10408, 0, SOUTH, 6)
        DAGANNOTH_7260(2479, 10430, 0, SOUTH, 2)
        BASILISK_417(2480, 10401, 0, SOUTH, 6)
        BASILISK_417(2481, 10405, 0, SOUTH, 6)
        BASILISK_417(2482, 10410, 0, SOUTH, 6)
        BASILISK_417(2485, 10406, 0, SOUTH, 6)
        BASILISK_417(2459, 10398, 0, SOUTH, 6)
        BASILISK_417(2459, 10403, 0, SOUTH, 6)
        BASILISK_417(2461, 10407, 0, SOUTH, 6)
        BASILISK_417(2463, 10399, 0, SOUTH, 6)
        BASILISK_417(2464, 10403, 0, SOUTH, 6)
        BASILISK_417(2467, 10398, 0, SOUTH, 6)
    }
}