package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9519Spawns : NPCSpawnsScript() {
    init {
        SCARG(2380, 3043, 0, SOUTH, 4)
        SWAMP_TOAD(2391, 3053, 0, SOUTH, 3)
        SWAMP_TOAD(2392, 3051, 0, SOUTH, 3)
        SWAMP_TOAD(2394, 3043, 0, SOUTH, 3)
        SWAMP_TOAD(2394, 3045, 0, SOUTH, 3)
        SWAMP_TOAD(2394, 3052, 0, SOUTH, 3)
        SWAMP_TOAD(2394, 3056, 0, SOUTH, 3)
        SWAMP_TOAD(2397, 3040, 0, SOUTH, 3)
        SWAMP_TOAD(2397, 3053, 0, <PERSON>O<PERSON><PERSON>, 3)
        SWAMP_TOAD(2398, 3047, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2399, 3046, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2400, 3040, 0, <PERSON><PERSON>UTH, 3)
        SWAMP_TOAD(2402, 3042, 0, SOUTH, 3)
        BREIVE(2410, 3060, 0, EAST, 0)
        GARGH(2411, 3044, 0, SOUTH, 7)
    }
}