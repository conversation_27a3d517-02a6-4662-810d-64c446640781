package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9615Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_7118(2371, 9183, 1, SOUT<PERSON>, 47)
        MANIACAL_MONKEY_7118(2372, 9157, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2376, 9156, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2376, 9180, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2377, 9159, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2378, 9176, 1, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_ARCHER(2378, 9210, 1, <PERSON><PERSON><PERSON><PERSON>, 0)
        MANI<PERSON>AL_<PERSON><PERSON>KEY_ARCHER(2380, 9213, 1, <PERSON><PERSON><PERSON><PERSON>, 0)
        <PERSON>NIACAL_MONKEY_7118(2384, 9171, 1, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIAC<PERSON>_M<PERSON><PERSON>Y_7118(2386, 9169, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2387, 9206, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2388, 9200, 1, SOUTH, 0)
        MA<PERSON>ACAL_MONKEY_ARCHER(2390, 9193, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2392, 9169, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2392, 9173, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2395, 9164, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2395, 9196, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2396, 9175, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2398, 9162, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2399, 9177, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2399, 9193, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2400, 9163, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2402, 9175, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2402, 9192, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2403, 9161, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2404, 9177, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2406, 9208, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2406, 9212, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2407, 9163, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2407, 9199, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2408, 9165, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2408, 9176, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2409, 9210, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2411, 9178, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2411, 9197, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2412, 9164, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2412, 9193, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2413, 9178, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2415, 9163, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2415, 9196, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2417, 9179, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2419, 9178, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2420, 9159, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2421, 9177, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2423, 9174, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2423, 9176, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2424, 9159, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2425, 9173, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2427, 9158, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2427, 9160, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2427, 9172, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2427, 9189, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2428, 9186, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2430, 9160, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2430, 9174, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2431, 9172, 1, SOUTH, 47)
    }
}