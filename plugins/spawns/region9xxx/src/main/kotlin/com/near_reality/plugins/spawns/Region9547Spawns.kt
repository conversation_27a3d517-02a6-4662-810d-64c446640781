package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9547Spawns : NPCSpawnsScript() {
    init {
        BIRD(2380, 4839, 0, <PERSON><PERSON>UT<PERSON>, 13)
        GULL(2381, 4843, 0, SOUTH, 7)
        GULL(2382, 4858, 0, SOUTH, 7)
        RABBIT_3421(2385, 4838, 0, SOUTH, 3)
        RABBIT_3422(2387, 4846, 0, SOUTH, 3)
        RABBIT_3422(2391, 4829, 0, SOUTH, 3)
        BIRD(2392, 4856, 0, SOUTH, 13)
        BIRD_5241(2396, 4858, 0, SOUTH, 26)
        RABBIT_3422(2399, 4856, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        GULL(2405, 4860, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        BIRD_5241(2407, 4827, 0, <PERSON><PERSON><PERSON><PERSON>, 26)
        GULL(2408, 4859, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        BIRD(2409, 4853, 0, S<PERSON><PERSON><PERSON>, 13)
        <PERSON>BB<PERSON>_3421(2413, 4836, 0, SOUTH, 3)
        GULL(2416, 4858, 0, SOUTH, 7)
        GULL(2417, 4832, 0, SOUTH, 7)
        GULL(2421, 4842, 0, SOUTH, 7)
    }
}