package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9273Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_4316(2310, 3697, 0, SOUTH, 0)
        FISHING_SPOT_4316(2311, 3704, 0, SOUTH, 0)
        DEVIN_MENDELBERG(2313, 3686, 0, SOUTH, 9)
        SKELETON_MAGE_4319(2316, 3685, 0, <PERSON>OUT<PERSON>, 6)
        SKELETON_MAGE_4319(2316, 3687, 0, SOUTH, 6)
        SKELETON_MAGE_4319(2317, 3689, 0, SOUTH, 6)
        SKELETON_MAGE_4319(2320, 3689, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SKELETON_MAGE_4319(2321, 3679, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SKELETON_MAGE_4319(2321, 3685, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SKELETON_MAGE_4319(2321, 3687, 0, SOUTH, 6)
        GEORGE_LAXMEISTER(2323, 3684, 0, SOUTH, 0)
        SKELETON_MAGE_4319(2325, 3671, 0, SOUTH, 6)
        FISHING_SPOT_4316(2326, 3700, 0, SOUTH, 0)
        ARNOLD_LYDSPOR(2330, 3691, 0, SOUTH, 2)
        SKELETON_MAGE_4319(2331, 3666, 0, SOUTH, 6)
        SKELETON_MAGE_4319(2331, 3671, 0, SOUTH, 6)
        SKELETON_MAGE_4319(2333, 3671, 0, SOUTH, 6)
        FISHING_SPOT_4316(2343, 3702, 0, SOUTH, 0)
        FRANKLIN_CARANOS(2344, 3667, 0, SOUTH, 5)
        RAMARA_DU_CROISSANT(2344, 3678, 0, SOUTH, 0)
        4300(2345, 3651, 0, SOUTH, 0)
        FISHING_SPOT_4316(2349, 3702, 0, SOUTH, 0)
        3365(2353, 3683, 0, SOUTH, 0)
        HERMAN_CARANOS(2354, 3683, 0, SOUTH, 0)
    }
}