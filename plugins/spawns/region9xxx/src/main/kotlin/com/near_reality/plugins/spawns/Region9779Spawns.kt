package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9779Spawns : NPCSpawnsScript() {
    init {
        4528(2436, 3315, 0, SOUTH, 2)
        WOMAN(2453, 3307, 0, SOUTH, 3)
        KHAZARD_TROOPER(2454, 3301, 0, SOUTH, 8)
        4971(2457, 3302, 0, SOUTH, 2)
        4558(2457, 3309, 0, SOUTH, 2)
        KHAZARD_TROOPER(2458, 3298, 0, SOUTH, 8)
        RAT_2854(2461, 3293, 0, SOUTH, 14)
        WOLF(2462, 3274, 0, <PERSON>O<PERSON><PERSON>, 6)
        RAT_2854(2462, 3300, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        CHILD_1133(2462, 3306, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        RAT_2854(2463, 3285, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        WOMAN_1140(2463, 3317, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        CHADWELL(2464, 3287, 0, SOUTH, 2)
        CHILD(2464, 3302, 0, SOUTH, 2)
        RAT_2854(2464, 3310, 0, SOUTH, 14)
        WOLF(2465, 3272, 0, SOUTH, 6)
        WOLF(2465, 3275, 0, SOUTH, 6)
        RAT_2854(2465, 3295, 0, SOUTH, 14)
        CHILD_1133(2465, 3305, 0, SOUTH, 3)
        MAN_1118(2465, 3307, 0, SOUTH, 2)
        RAT_2854(2466, 3294, 0, SOUTH, 14)
        RAT_2854(2466, 3299, 0, SOUTH, 14)
        CHILD(2466, 3300, 0, SOUTH, 2)
        CHILD(2466, 3307, 0, SOUTH, 2)
        4267(2466, 3318, 0, SOUTH, 5)
        CHILD(2467, 3306, 0, SOUTH, 2)
        RAT_2854(2467, 3322, 0, SOUTH, 14)
        WOMAN_1142(2468, 3290, 0, SOUTH, 4)
        CIVILIAN(2468, 3304, 0, SOUTH, 0)
        CHILD(2468, 3315, 0, SOUTH, 2)
        RAT_2854(2468, 3326, 0, SOUTH, 14)
        CHILD_1133(2469, 3295, 0, SOUTH, 3)
        CIVILIAN_3506(2469, 3309, 0, SOUTH, 2)
        CHILD(2469, 3319, 0, SOUTH, 2)
        WOLF(2470, 3272, 0, SOUTH, 6)
        WOMAN_1131(2470, 3290, 0, SOUTH, 4)
        RAT_2854(2470, 3314, 0, SOUTH, 14)
        RAT_2854(2470, 3318, 0, SOUTH, 14)
        RAT_2854(2470, 3321, 0, SOUTH, 14)
        RAT_2854(2471, 3298, 0, SOUTH, 14)
        RAT_2854(2471, 3316, 0, SOUTH, 14)
        CHILD(2471, 3323, 0, SOUTH, 2)
        WOLF(2472, 3274, 0, SOUTH, 6)
        WOMAN_1131(2472, 3296, 0, SOUTH, 4)
        RAT_2854(2472, 3301, 0, SOUTH, 14)
        WOMAN_1139(2472, 3307, 0, SOUTH, 4)
        RAT_2854(2472, 3314, 0, SOUTH, 14)
        RAT_2854(2472, 3324, 0, SOUTH, 14)
        WOMAN_1130(2473, 3288, 0, SOUTH, 4)
        4262(2473, 3290, 0, SOUTH, 0)
        CHILD(2473, 3305, 0, SOUTH, 2)
        RAT_2854(2473, 3326, 0, SOUTH, 14)
        CHILD(2474, 3296, 0, SOUTH, 2)
        RAT_2854(2474, 3301, 0, SOUTH, 14)
        RAT_2854(2474, 3306, 0, SOUTH, 14)
        CIVILIAN_3506(2475, 3298, 0, SOUTH, 2)
        RAT_2854(2475, 3303, 0, SOUTH, 14)
        WOMAN_1142(2475, 3325, 0, SOUTH, 4)
        RAT_2854(2476, 3293, 0, SOUTH, 14)
        CHILD(2476, 3307, 0, SOUTH, 2)
        CIVILIAN_3507(2476, 3313, 0, SOUTH, 2)
        CIVILIAN(2476, 3317, 0, SOUTH, 0)
        RAT_2854(2476, 3322, 0, SOUTH, 14)
        WOLF(2477, 3275, 0, SOUTH, 6)
        RAT_2854(2477, 3286, 0, SOUTH, 14)
        RAT_2854(2477, 3298, 0, SOUTH, 14)
        RAT_2854(2477, 3301, 0, SOUTH, 14)
        CHILD_1133(2477, 3319, 0, SOUTH, 3)
        RAT_2854(2477, 3324, 0, SOUTH, 14)
        RAT_2854(2478, 3285, 0, SOUTH, 14)
        CIVILIAN(2478, 3291, 0, SOUTH, 0)
        RAT_2854(2478, 3304, 0, SOUTH, 14)
        CHILD(2478, 3323, 0, SOUTH, 2)
        WOMAN_1142(2479, 3311, 0, SOUTH, 4)
        RAT_2854(2479, 3313, 0, SOUTH, 14)
        RAT_2854(2480, 3293, 0, SOUTH, 14)
        CHILD(2480, 3296, 0, SOUTH, 2)
        WOMAN(2480, 3300, 0, SOUTH, 3)
        RAT_2854(2480, 3306, 0, SOUTH, 14)
        RAT_2854(2480, 3315, 0, SOUTH, 14)
        CIVILIAN_3507(2481, 3289, 0, SOUTH, 2)
        RAT_2854(2481, 3295, 0, SOUTH, 14)
        RAT_2854(2481, 3300, 0, SOUTH, 14)
        RAT_2854(2481, 3307, 0, SOUTH, 14)
        CHILD(2481, 3313, 0, SOUTH, 2)
        RAT_2854(2482, 3284, 0, SOUTH, 14)
        WOMAN_1141(2482, 3286, 0, SOUTH, 2)
        MAN_1118(2482, 3293, 0, SOUTH, 2)
        WOMAN_1140(2482, 3296, 0, SOUTH, 2)
        RAT_2854(2482, 3302, 0, SOUTH, 14)
        CHILD_1133(2482, 3311, 0, SOUTH, 3)
        RAT_2854(2483, 3285, 0, SOUTH, 14)
        WOMAN_1131(2483, 3318, 0, SOUTH, 4)
        WOMAN_1131(2483, 3323, 0, SOUTH, 4)
        RAT_2854(2484, 3294, 0, SOUTH, 14)
        RAT_2854(2484, 3300, 0, SOUTH, 14)
        4267(2484, 3302, 0, SOUTH, 5)
        RAT_2854(2484, 3304, 0, SOUTH, 14)
        RAT_2854(2484, 3307, 0, SOUTH, 14)
        CHILD(2485, 3312, 0, SOUTH, 2)
        WOMAN_1140(2485, 3315, 0, SOUTH, 2)
        4262(2485, 3321, 0, SOUTH, 0)
        RAT_2854(2486, 3281, 0, SOUTH, 14)
        RAT_2854(2486, 3285, 0, SOUTH, 14)
        RAT_2854(2486, 3292, 0, SOUTH, 14)
        RAT_2854(2486, 3296, 0, SOUTH, 14)
        RAT_2854(2486, 3300, 0, SOUTH, 14)
        RAT_2854(2486, 3313, 0, SOUTH, 14)
        RAT_2854(2487, 3286, 0, SOUTH, 14)
        RAT_2854(2487, 3303, 0, SOUTH, 14)
        CIVILIAN_3506(2487, 3308, 0, SOUTH, 2)
        RAT_2854(2488, 3305, 0, SOUTH, 14)
        RAT_2854(2488, 3319, 0, SOUTH, 14)
        RAT_2854(2489, 3284, 0, SOUTH, 14)
        WOMAN_1141(2489, 3295, 0, SOUTH, 2)
        RAT_2854(2489, 3302, 0, SOUTH, 14)
        CIVILIAN_3506(2490, 3289, 0, SOUTH, 2)
        WOMAN_1131(2490, 3293, 0, SOUTH, 4)
        RAT_2854(2490, 3307, 0, SOUTH, 14)
        CIVILIAN_3507(2490, 3313, 0, SOUTH, 2)
        RAT_2854(2490, 3321, 0, SOUTH, 14)
        RAT_2854(2490, 3323, 0, SOUTH, 14)
        CIVILIAN_3507(2491, 3300, 0, SOUTH, 2)
        RAT_2854(2492, 3303, 0, SOUTH, 14)
        CIVILIAN(2492, 3312, 0, SOUTH, 0)
        WOMAN_1139(2492, 3314, 0, SOUTH, 4)
        5285(2495, 3312, 0, SOUTH, 18)
    }
}