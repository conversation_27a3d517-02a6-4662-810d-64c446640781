package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9541Spawns : NPCSpawnsScript() {
    init {
        3367(2373, 4447, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        3367(2375, 4448, 0, SOUTH, 2)
        3367(2376, 4445, 0, SOUTH, 2)
        FAIRY_SHOP_ASSISTANT(2376, 4447, 0, SOUTH, 2)
        3367(2377, 4450, 0, SOUTH, 2)
        FAIRY_SHOP_KEEPER(2378, 4447, 0, <PERSON>OUT<PERSON>, 2)
        OTHERWORLDLY_BEING(2381, 4425, 0, SOUTH, 3)
        3367(2381, 4459, 0, <PERSON>OUT<PERSON>, 2)
        OTHERWORLDLY_BEING(2382, 4421, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        OTHERWORLDLY_BEING(2382, 4429, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        3367(2383, 4424, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        3367(2383, 4455, 0, SO<PERSON>H, 2)
        BANKER_3092(2383, 4456, 0, SOUTH, 4)
        BANKER_3092(2383, 4459, 0, SOUTH, 4)
        BANKER_3092(2385, 4457, 0, SOUTH, 4)
        3367(2385, 4458, 0, SOUTH, 2)
        OTHERWORLDLY_BEING(2386, 4419, 0, SOUTH, 3)
        3367(2386, 4431, 0, SOUTH, 2)
        FAIRY_CHEF(2386, 4439, 0, SOUTH, 2)
        BANKER_3092(2386, 4460, 0, SOUTH, 4)
        3367(2388, 4425, 0, SOUTH, 2)
        OTHERWORLDLY_BEING(2388, 4428, 0, SOUTH, 3)
        2968(2388, 4470, 0, SOUTH, 4)
        OTHERWORLDLY_BEING(2389, 4422, 0, SOUTH, 3)
        3367(2391, 4453, 0, SOUTH, 2)
        3367(2393, 4447, 0, SOUTH, 2)
        3367(2394, 4440, 0, SOUTH, 2)
        BUTTERFLY_235(2394, 4453, 0, SOUTH, 7)
        BUTTERFLY_235(2394, 4457, 0, SOUTH, 7)
        3367(2395, 4461, 0, SOUTH, 2)
        5236(2396, 4455, 0, SOUTH, 5)
        3367(2396, 4457, 0, SOUTH, 2)
        3367(2398, 4448, 0, SOUTH, 2)
        5424(2398, 4453, 0, SOUTH, 4)
        BUTTERFLY_235(2406, 4447, 0, SOUTH, 7)
        3367(2408, 4445, 0, SOUTH, 2)
        1860(2410, 4434, 0, SOUTH, 5)
        SHEEP_5844(2410, 4456, 0, SOUTH, 4)
        3367(2412, 4437, 0, SOUTH, 2)
        536(2412, 4470, 0, SOUTH, 0)
        536(2414, 4467, 0, SOUTH, 0)
        SHEEP_5844(2415, 4429, 0, SOUTH, 4)
        3367(2415, 4438, 0, SOUTH, 2)
        SHEEP_5843(2415, 4451, 0, SOUTH, 5)
        BUTTERFLY(2415, 4466, 0, SOUTH, 7)
        536(2415, 4475, 0, SOUTH, 0)
        SHEEP_5843(2416, 4435, 0, SOUTH, 5)
        3367(2418, 4437, 0, SOUTH, 2)
        3367(2418, 4447, 0, SOUTH, 2)
        BUTTERFLY(2419, 4427, 0, SOUTH, 7)
        3367(2419, 4465, 0, SOUTH, 2)
        536(2419, 4467, 0, SOUTH, 0)
        536(2419, 4474, 0, SOUTH, 0)
        BUTTERFLY(2421, 4468, 0, SOUTH, 7)
        SHEEP_5843(2423, 4432, 0, SOUTH, 5)
        3367(2423, 4461, 0, SOUTH, 2)
        WANDERING_IMPLING(2424, 4436, 0, SOUTH, 3)
        BUTTERFLY(2426, 4431, 0, SOUTH, 7)
        3367(2426, 4433, 0, SOUTH, 2)
        FAIRY_AERYKA(2426, 4442, 0, SOUTH, 5)
        3367(2427, 4461, 0, SOUTH, 2)
        3367(2431, 4457, 0, SOUTH, 2)
    }
}