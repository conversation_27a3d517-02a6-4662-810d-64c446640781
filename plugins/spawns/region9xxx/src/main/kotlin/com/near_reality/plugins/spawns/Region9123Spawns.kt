package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9123Spawns : NPCSpawnsScript() {
    init {
        SPIDER_8137(2257, 10463, 0, SOUTH, 5)
        SPIDER_8137(2261, 10472, 0, <PERSON>OUT<PERSON>, 5)
        SPIDER_8137(2267, 10478, 0, SOUTH, 5)
        SPIDER_8137(2273, 10472, 0, SOUTH, 5)
        SPIDER_8137(2277, 10456, 0, <PERSON><PERSON>UT<PERSON>, 5)
        SPIDER_8137(2283, 10483, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SPIDER_8137(2284, 10466, 0, SOUTH, 5)
    }
}