package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9824Spawns : NPCSpawnsScript() {
    init {
        SPIDER_4561(2457, 6186, 0, SOUT<PERSON>, 5)
        SPIDER_4561(2458, 6202, 0, SOUT<PERSON>, 5)
        SPIDER_4561(2460, 6182, 0, SOUTH, 5)
        SPIDER_4561(2462, 6166, 0, <PERSON>OUTH, 5)
        SPIDER_4561(2462, 6175, 0, SOUTH, 5)
        SPIDER_4561(2462, 6188, 0, SOUT<PERSON>, 5)
        SPIDER_4561(2464, 6181, 0, SOUTH, 5)
        SPIDER_4561(2465, 6176, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SPIDER_4561(2465, 6186, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SPIDER_4561(2466, 6194, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SPIDER_4561(2467, 6163, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SPIDER_4561(2467, 6169, 0, SOUTH, 5)
        SPIDER_4561(2467, 6181, 0, SOUTH, 5)
        SPIDER_4561(2467, 6202, 0, SOUTH, 5)
        SPIDER_4561(2469, 6185, 0, SOUTH, 5)
        SPIDER_4561(2472, 6167, 0, SOUTH, 5)
        SPIDER_4561(2472, 6193, 0, SOUTH, 5)
        SPIDER_4561(2473, 6176, 0, SOUTH, 5)
        SPIDER_4561(2474, 6161, 0, SOUTH, 5)
        SPIDER_4561(2474, 6169, 0, SOUTH, 5)
        SPIDER_4561(2474, 6178, 0, SOUTH, 5)
        SPIDER_4561(2474, 6203, 0, SOUTH, 5)
        SPIDER_4561(2475, 6166, 0, SOUTH, 5)
        SPIDER_4561(2475, 6175, 0, SOUTH, 5)
        SPIDER_4561(2475, 6188, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2475, 6204, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2476, 6202, 0, SOUTH, 5)
        BLESSED_SPIDER(2477, 6182, 0, SOUTH, 5)
        SPIDER_4561(2477, 6185, 0, SOUTH, 5)
        BLESSED_SPIDER(2477, 6187, 0, SOUTH, 5)
        SPIDER_4561(2477, 6204, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2477, 6206, 0, SOUTH, 5)
        SPIDER_4561(2478, 6163, 0, SOUTH, 5)
        BLESSED_SPIDER(2478, 6184, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2478, 6199, 0, SOUTH, 5)
        BLESSED_SPIDER(2479, 6183, 0, SOUTH, 5)
        BLESSED_SPIDER(2479, 6184, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2479, 6198, 0, SOUTH, 5)
        BLESSED_SPIDER(2480, 6185, 0, SOUTH, 5)
        BLESSED_SPIDER(2480, 6187, 0, SOUTH, 5)
        SPIDER_4561(2480, 6197, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2480, 6202, 0, SOUTH, 5)
        BLESSED_SPIDER(2481, 6184, 0, SOUTH, 5)
        SPIDER_4561(2481, 6187, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2481, 6204, 0, SOUTH, 5)
        SPIDER_4561(2482, 6166, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2482, 6190, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2482, 6191, 0, SOUTH, 5)
        SPIDER_4561(2482, 6202, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2483, 6190, 0, SOUTH, 5)
        SPIDER_4561(2483, 6193, 0, SOUTH, 5)
        SPIDER_4561(2483, 6195, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2483, 6198, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2483, 6199, 0, SOUTH, 5)
        SPIDER_4561(2484, 6169, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2484, 6197, 0, SOUTH, 5)
        SPIDER_4561(2485, 6173, 0, SOUTH, 5)
        SPIDER_4561(2485, 6177, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2485, 6190, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2485, 6204, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2486, 6190, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2486, 6204, 0, SOUTH, 5)
        SPIDER_4561(2486, 6206, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2487, 6200, 0, SOUTH, 5)
        SPIDER_4561(2488, 6167, 0, SOUTH, 5)
        SPIDER_4561(2488, 6179, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2488, 6199, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2488, 6200, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2488, 6202, 0, SOUTH, 5)
        SPIDER_4561(2489, 6172, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2489, 6194, 0, SOUTH, 5)
        SPIDER_4561(2489, 6199, 0, SOUTH, 5)
        SPIDER_4561(2490, 6182, 0, SOUTH, 5)
        SPIDER_4561(2490, 6192, 0, SOUTH, 5)
        SPIDER_4561(2491, 6193, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2491, 6196, 0, SOUTH, 5)
        SPIDER_4561(2491, 6198, 0, SOUTH, 5)
        SPIDER_4561(2491, 6204, 0, SOUTH, 5)
        SPIDER_4561(2492, 6185, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2492, 6200, 0, SOUTH, 5)
        SPIDER_4561(2492, 6202, 0, SOUTH, 5)
    }
}