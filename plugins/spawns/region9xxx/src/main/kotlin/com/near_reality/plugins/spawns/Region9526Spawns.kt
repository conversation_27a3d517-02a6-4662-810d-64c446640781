package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9526Spawns : NPCSpawnsScript() {
    init {
        6207(2368, 3486, 0, SOUTH, 3)
        4297(2370, 3484, 0, SOUTH, 4)
        BUTTERFLY_237(2371, 3460, 0, SOUTH, 7)
        BUTTERFLY_237(2372, 3456, 0, SOUTH, 7)
        BUTTERFLY_236(2374, 3469, 0, <PERSON>OUTH, 9)
        BUTTERFLY_236(2376, 3466, 0, SOUTH, 9)
        GNOME_WOMAN_6087(2378, 3482, 0, SOUTH, 5)
        GNOME_WOMAN(2379, 3482, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GNOME_WOMAN(2381, 3496, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GNOME_WOMAN_6087(2383, 3496, 0, <PERSON><PERSON>UTH, 5)
        GNOME_TROOP(2384, 3481, 0, SOUTH, 3)
        GNOME_BALL_REFEREE(2384, 3488, 0, SOUTH, 2)
        GNOME_WOMAN(2384, 3497, 0, SOUTH, 5)
        GNOME_TROOP(2388, 3473, 0, SOUTH, 3)
        GNOME_WOMAN(2391, 3476, 0, SOUTH, 5)
        GNOME_BALLER_3151(2391, 3484, 0, SOUTH, 3)
        GNOME_BALLER_3151(2391, 3487, 0, SOUTH, 3)
        GNOME_BALLER_3151(2391, 3489, 0, SOUTH, 3)
        GNOME_BALLER_3151(2391, 3492, 0, SOUTH, 3)
        GNOME_CHILD(2392, 3475, 0, SOUTH, 3)
        GNOME_WINGER(2393, 3483, 0, SOUTH, 4)
        GNOME_WINGER(2393, 3493, 0, SOUTH, 4)
        GNOME_CHILD(2394, 3506, 0, SOUTH, 3)
        CHEERLEADER(2395, 3495, 0, SOUTH, 0)
        GNOME_CHILD(2396, 3471, 0, SOUTH, 3)
        6116(2396, 3481, 0, SOUTH, 2)
        GNOME_BALLER_3147(2396, 3485, 0, SOUTH, 2)
        GNOME_BALLER_3147(2396, 3487, 0, SOUTH, 2)
        GNOME_BALLER_3147(2396, 3489, 0, SOUTH, 2)
        GNOME_BALLER_3147(2396, 3491, 0, SOUTH, 2)
        CHEERLEADER(2397, 3495, 0, SOUTH, 0)
        GNOME_BALLER(2400, 3483, 0, SOUTH, 2)
        GNOME_BALLER(2400, 3485, 0, SOUTH, 2)
        GNOME_BALLER(2400, 3487, 0, SOUTH, 2)
        GNOME_BALLER(2400, 3489, 0, SOUTH, 2)
        GNOME_BALLER(2400, 3491, 0, SOUTH, 2)
        GNOME_BALLER(2400, 3493, 0, SOUTH, 2)
        GNOME_WOMAN_6087(2402, 3507, 0, SOUTH, 5)
        GNOME_CHILD(2405, 3499, 0, SOUTH, 3)
        GNOME_WOMAN_6087(2406, 3476, 0, SOUTH, 5)
        GNOME_COACH(2406, 3498, 0, SOUTH, 5)
        GNOME_TROOP_4974(2409, 3507, 0, SOUTH, 4)
        GNOME_WOMAN(2410, 3496, 0, SOUTH, 5)
        GNOME_CHILD(2416, 3487, 0, SOUTH, 3)
        SARBLE(2417, 3517, 0, SOUTH, 3)
        GNOME_WOMAN(2421, 3481, 0, SOUTH, 5)
        BUTTERFLY_237(2422, 3467, 0, SOUTH, 7)
        GNOME_WOMAN_6087(2422, 3485, 0, SOUTH, 5)
        BUTTERFLY_235(2422, 3489, 0, SOUTH, 7)
        GNOME_WOMAN_6087(2382, 3506, 1, SOUTH, 5)
        7194(2383, 3509, 1, SOUTH, 2)
        7188(2388, 3514, 1, SOUTH, 2)
        7180(2390, 3514, 1, SOUTH, 2)
        GNOME_6095(2394, 3500, 1, SOUTH, 4)
        WURBEL(2395, 3477, 1, SOUTH, 5)
        GNOME_WOMAN(2397, 3514, 1, SOUTH, 5)
        GNOME_6096(2400, 3514, 1, SOUTH, 2)
        GNOME_GUARD_6081(2409, 3470, 1, SOUTH, 5)
        GNOME_GUARD_6081(2416, 3466, 1, SOUTH, 5)
        GNOME_6095(2417, 3483, 1, SOUTH, 4)
        GNOME_6094(2417, 3493, 1, SOUTH, 2)
        DAMWIN(2417, 3496, 1, SOUTH, 2)
        BARMAN_6532(2417, 3498, 1, SOUTH, 2)
        GNOME_WOMAN_6087(2418, 3472, 1, SOUTH, 5)
        GNOME_6096(2418, 3485, 1, SOUTH, 2)
        GNOME_6094(2418, 3495, 1, SOUTH, 2)
        GNOME_GUARD_6081(2420, 3466, 1, SOUTH, 5)
        BURKOR(2420, 3474, 1, SOUTH, 2)
        GNOME_GUARD_6081(2412, 3471, 3, SOUTH, 5)
        GNOME_TROOP_4974(2412, 3474, 3, SOUTH, 4)
        GNOME_GUARD_6081(2418, 3471, 3, SOUTH, 5)
        GNOME_TROOP(2418, 3474, 3, SOUTH, 3)
    }
}