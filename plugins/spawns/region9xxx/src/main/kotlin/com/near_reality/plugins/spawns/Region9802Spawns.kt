package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9802Spawns : NPCSpawnsScript() {
    init {
        FROG_5432(2455, 4784, 0, SOUTH, 5)
        FROG_5432(2458, 4773, 0, SOUTH, 5)
        FROG_5432(2463, 4778, 0, SOUTH, 5)
        FROG_5431(2463, 4782, 0, SOUTH, 5)
        FROG_5432(2464, 4770, 0, SOUTH, 5)
        FROG_5432(2467, 4774, 0, SOUTH, 5)
        FROG_5432(2468, 4789, 0, SOUTH, 5)
        FROG_5432(2474, 4781, 0, SOUTH, 5)
    }
}