package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9780Spawns : NPCSpawnsScript() {
    init {
        MOUNTED_TERRORBIRD_GNOME(2432, 3387, 0, SOUTH, 11)
        WOLF(2437, 3336, 0, SOUTH, 6)
        JORRAL(2437, 3347, 0, WEST, 0)
        WOLF(2439, 3333, 0, SOUTH, 6)
        WOLF(2439, 3335, 0, SOUTH, 6)
        WOLF(2440, 3333, 0, SOUTH, 6)
        WOLF(2440, 3335, 0, SOUTH, 6)
        MOUNTED_TERRORBIRD_GNOME(2442, 3388, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        HOBGOBLIN_3050(2443, 3359, 0, SOUT<PERSON>, 3)
        HOBGOBLIN_3050(2445, 3359, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        HOBGOBLIN_3049(2445, 3362, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGO<PERSON><PERSON>_3050(2446, 3357, 0, SOUTH, 3)
        HOBGOBLIN_3049(2447, 3361, 0, SOUTH, 13)
        HOBGOBLIN_3050(2448, 3329, 0, SOUTH, 3)
        HOBGOBLIN_3050(2448, 3331, 0, SOUTH, 3)
        HOBGOBLIN_3050(2451, 3330, 0, SOUTH, 3)
        MOUNTED_TERRORBIRD_GNOME(2453, 3363, 0, SOUTH, 11)
        6131(2457, 3357, 0, SOUTH, 12)
        FEMI(2459, 3382, 0, SOUTH, 2)
        GNOME_GUARD_6081(2459, 3385, 0, SOUTH, 5)
        GNOME_GUARD_6082(2460, 3382, 0, SOUTH, 5)
        GNOME_GUARD_6082(2462, 3382, 0, SOUTH, 5)
        MOUNTED_TERRORBIRD_GNOME(2463, 3375, 0, SOUTH, 11)
        GNOME_GUARD_6081(2463, 3385, 0, SOUTH, 5)
        BUTTERFLY(2478, 3373, 0, SOUTH, 7)
        BUTTERFLY(2479, 3381, 0, SOUTH, 7)
        BUTTERFLY(2481, 3384, 0, SOUTH, 7)
        BUTTERFLY(2490, 3371, 0, SOUTH, 7)
    }
}