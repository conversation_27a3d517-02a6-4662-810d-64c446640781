package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9291Spawns : NPCSpawnsScript() {
    init {
        SKELETON_8139(2321, 4829, 0, <PERSON>OUT<PERSON>, 5)
        SKELETON_8139(2322, 4827, 0, SOUTH, 5)
        SKELETON_8139(2322, 4833, 0, SOUTH, 5)
        SKELETON_8139(2323, 4835, 0, SOUTH, 5)
        SKELETON_8139(2324, 4820, 0, SOUTH, 5)
        SKELETON_8139(2324, 4822, 0, <PERSON>O<PERSON><PERSON>, 5)
        SKELETON_8139(2325, 4840, 0, SOUTH, 5)
        SKELETON_8139(2326, 4842, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKELETON_8139(2327, 4817, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKELETON_8139(2329, 4816, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKELETON_8139(2331, 4844, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKELETON_8139(2333, 4814, 0, SOUTH, 5)
        SKELETON_8139(2333, 4845, 0, SOUTH, 5)
        SKELETON_8139(2335, 4814, 0, SOUTH, 5)
        SKELETON_8139(2339, 4844, 0, SOUTH, 5)
        SK<PERSON>ETON_8139(2340, 4815, 0, SOUTH, 5)
        SKELETON_8139(2341, 4817, 0, SOUTH, 5)
        SKELETON_8139(2341, 4843, 0, SOUTH, 5)
        SKELETON_8139(2345, 4820, 0, SOUTH, 5)
        SKELETON_8139(2345, 4822, 0, SOUTH, 5)
        SKELETON_8139(2345, 4837, 0, SOUTH, 5)
        SKELETON_8139(2346, 4835, 0, SOUTH, 5)
        SKELETON_8139(2348, 4828, 0, SOUTH, 5)
        SKELETON_8139(2348, 4830, 0, SOUTH, 5)
    }
}