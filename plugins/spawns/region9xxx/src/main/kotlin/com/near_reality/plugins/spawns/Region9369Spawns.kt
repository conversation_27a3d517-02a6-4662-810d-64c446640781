package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9369Spawns : NPCSpawnsScript() {
    init {
        8900(2308, 9804, 0, SOUTH, 5)
        8896(2312, 9801, 0, SOUTH, 5)
        8820(2312, 9814, 0, SOUTH, 5)
        3104(2313, 9853, 0, SOUTH, 2)
        NILOOF(2315, 9806, 0, SOUTH, 5)
        3104(2315, 9851, 0, SOUTH, 2)
        8916(2318, 9808, 0, SOUTH, 5)
        3104(2318, 9847, 0, SOUTH, 2)
        3104(2319, 9852, 0, SOUTH, 2)
        8905(2321, 9798, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        3104(2322, 9846, 0, SOUTH, 2)
        KLANK(2323, 9804, 0, SOUTH, 5)
        3104(2323, 9843, 0, SOUTH, 2)
        3104(2323, 9852, 0, SO<PERSON><PERSON>, 2)
        KAMEN(2325, 9799, 0, SOUTH, 5)
        3104(2326, 9848, 0, SOUTH, 2)
        8909(2328, 9796, 0, SOUTH, 5)
        8912(2328, 9803, 0, SOUTH, 5)
        3104(2331, 9852, 0, SOUTH, 2)
        3104(2333, 9851, 0, SOUTH, 2)
        3104(2334, 9853, 0, SOUTH, 2)
        3104(2335, 9847, 0, SOUTH, 2)
        3104(2338, 9844, 0, SOUTH, 2)
        3104(2338, 9851, 0, SOUTH, 2)
        3104(2340, 9848, 0, SOUTH, 2)
        3104(2341, 9852, 0, SOUTH, 2)
        3104(2342, 9840, 0, SOUTH, 2)
        3104(2342, 9841, 0, SOUTH, 2)
        3104(2343, 9849, 0, SOUTH, 2)
        3104(2345, 9851, 0, SOUTH, 2)
        3104(2346, 9854, 0, SOUTH, 2)
        3104(2347, 9854, 0, SOUTH, 2)
        3104(2348, 9843, 0, SOUTH, 2)
    }
}