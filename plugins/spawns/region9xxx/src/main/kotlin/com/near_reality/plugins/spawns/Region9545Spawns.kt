package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9545Spawns : NPCSpawnsScript() {
    init {
        BOULDER_3967(2373, 4692, 0, SOUTH, 5)
        SAN_TOJALON(2384, 4706, 0, SOUTH, 5)
        BOULDER_3967(2386, 4689, 0, SOUTH, 5)
        BOULDER_3967(2392, 4678, 0, SOUTH, 5)
        SAN_TOJALON(2393, 4713, 0, SOUTH, 5)
        SAN_TOJALON(2394, 4731, 0, SOUTH, 5)
        SAN_TOJALON(2395, 4721, 0, SOUTH, 5)
        IRVIG_SENAY(2401, 4705, 0, <PERSON>O<PERSON><PERSON>, 5)
        IRVIG_SENAY(2405, 4712, 0, <PERSON>O<PERSON><PERSON>, 5)
        RANALPH_DEVERE(2408, 4698, 0, <PERSON>O<PERSON><PERSON>, 5)
        LESSER_DEMON_2007(2412, 4677, 0, SOUTH, 4)
        IRVIG_SENAY(2412, 4723, 0, SOUTH, 5)
        LESSER_DEMON_2006(2413, 4681, 0, SOUTH, 6)
        IRVIG_SENAY(2414, 4731, 0, SOUTH, 5)
        LESSER_DEMON(2416, 4684, 0, SOUTH, 4)
        RANALPH_DEVERE(2417, 4699, 0, SOUTH, 5)
        RANALPH_DEVERE(2424, 4701, 0, SOUTH, 5)
        RANALPH_DEVERE(2428, 4696, 0, SOUTH, 5)
    }
}