package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9295Spawns : NPCSpawnsScript() {
    init {
        GIANT_LOBSTER_4800(2312, 5086, 0, SOUTH, 5)
        HOBGOBLIN_4805(2313, 5103, 0, SOUTH, 5)
        GIANT_LOBSTER_4800(2314, 5068, 0, SOUTH, 5)
        HOBGOBLIN_4805(2315, 5105, 0, SOUTH, 5)
        HOBGOBLIN_4805(2316, 5109, 0, SOUTH, 5)
        HOBGOBLIN_4805(2318, 5104, 0, SOUTH, 5)
        GIANT_LOBSTER_4800(2327, 5065, 0, SOUTH, 5)
        GIANT_LOBSTER_4800(2327, 5093, 0, SOUTH, 5)
        GIANT_LOBSTER_4800(2334, 5086, 0, SOUTH, 5)
        GIANT_LOBSTER_4800(2340, 5076, 0, SOUTH, 5)
        GIANT_LOBSTER_4800(2340, 5108, 0, SOUTH, 5)
        SEA_SLUG(2351, 5093, 0, SOUTH, 5)
    }
}