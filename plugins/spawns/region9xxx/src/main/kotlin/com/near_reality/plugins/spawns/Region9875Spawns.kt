package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9875Spawns : NPCSpawnsScript() {
    init {
        ZOGRE_868(2449, 9429, 0, SOUTH, 5)
        ZOGRE_869(2449, 9434, 0, SOUTH, 5)
        ZOGRE_871(2452, 9440, 0, SOUTH, 5)
        ZOGRE_870(2455, 9435, 0, SOUTH, 5)
        ZOGRE_868(2436, 9452, 2, SOUTH, 5)
        ZOGRE_868(2436, 9465, 2, SOUTH, 5)
        ZOGRE_869(2437, 9429, 2, SOUTH, 5)
        ZOGRE_869(2440, 9438, 2, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKOGRE_879(2442, 9436, 2, <PERSON>OUTH, 5)
        ZOGRE(2448, 9442, 2, <PERSON><PERSON>UTH, 5)
        ZOGRE_868(2450, 9460, 2, SOUTH, 5)
        ZOGRE_868(2455, 9440, 2, SOUTH, 5)
        ZOGRE_870(2457, 9420, 2, SOUTH, 5)
        ZOGRE_869(2458, 9408, 2, SOUTH, 5)
        SKOGRE_879(2458, 9413, 2, SOUTH, 5)
        ZOGRE_870(2462, 9431, 2, SOUTH, 5)
        ZOGRE(2465, 9454, 2, SOUTH, 5)
        SKOGRE_879(2470, 9435, 2, SOUTH, 5)
        ZOGRE(2471, 9422, 2, SOUTH, 5)
        SKOGRE_879(2472, 9460, 2, SOUTH, 5)
        ZOGRE_867(2473, 9430, 2, SOUTH, 5)
        ZOGRE_868(2473, 9439, 2, SOUTH, 5)
        ZOGRE_867(2478, 9442, 2, SOUTH, 5)
        ZOGRE_870(2482, 9422, 2, SOUTH, 5)
        ZOGRE_868(2483, 9408, 2, SOUTH, 5)
        SKOGRE_879(2483, 9413, 2, SOUTH, 5)
        SKOGRE_879(2485, 9447, 2, SOUTH, 5)
        ZOGRE_867(2487, 9455, 2, SOUTH, 5)
    }
}