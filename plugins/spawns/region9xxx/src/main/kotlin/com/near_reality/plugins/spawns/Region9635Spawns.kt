package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9635Spawns : NPCSpawnsScript() {
    init {
        DAGANNOTH_7260(2410, 10435, 0, SOUTH, 2)
        DAGANNOTH_7259(2413, 10433, 0, SOUTH, 4)
        DAGANNOTH_7259(2415, 10436, 0, SOUTH, 4)
        DAGANNOTH_7260(2417, 10432, 0, SOUTH, 2)
        DAGANNOTH_7259(2422, 10433, 0, SOUTH, 4)
    }
}