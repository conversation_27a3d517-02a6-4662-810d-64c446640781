package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9270Spawns : NPCSpawnsScript() {
    init {
        FERRET(2305, 3516, 0, SOUTH, 4)
        FERRET(2309, 3515, 0, SOUTH, 4)
        FERRET(2311, 3506, 0, SOUTH, 4)
        FERRET(2311, 3519, 0, SOUTH, 4)
        FERRET(2313, 3510, 0, SOUTH, 4)
        FERRET(2316, 3500, 0, SOUTH, 4)
        FERRET(2316, 3511, 0, SOUTH, 4)
        FERRET(2317, 3505, 0, SOUTH, 4)
        2024(2318, 3503, 0, SOUTH, 4)
        EAGLE_5317(2319, 3477, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        FERRET(2319, 3518, 0, <PERSON><PERSON>UT<PERSON>, 4)
        EAGLE_5317(2323, 3485, 0, <PERSON>OUTH, 0)
        EAGLE_5317(2334, 3485, 0, SOUTH, 0)
        EAG<PERSON>_5317(2334, 3495, 0, SOUTH, 0)
    }
}