package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9004Spawns : NPCSpawnsScript() {
    init {
        BIRD_10541(2242, 2830, 0, SOUTH, 5)
        SQUIRREL_1418(2244, 2850, 0, SOUTH, 10)
        BUTTERFLY_238(2248, 2858, 0, SOUTH, 0)
        BUTTERFLY_238(2256, 2828, 0, SOUTH, 0)
        IMP_5007(2258, 2831, 0, SOUTH, 100)
        IMP_5007(2267, 2842, 0, SOUTH, 100)
        SQUIRREL_1417(2268, 2845, 0, SOUTH, 9)
        IMP_5007(2269, 2853, 0, SOUTH, 100)
        BIRD_10541(2271, 2875, 0, <PERSON>O<PERSON><PERSON>, 5)
        CRAB_8733(2275, 2820, 0, SOUTH, 5)
        IMP_5007(2275, 2833, 0, SOUTH, 100)
        BIRD_10541(2277, 2849, 0, SOUTH, 5)
        FISHING_SPOT_10514(2280, 2837, 0, SOUTH, 5)
        CRAB_1553(2283, 2826, 0, SOUTH, 4)
        BUTTERFLY_238(2284, 2874, 0, SOUTH, 0)
        SQUIRREL(2287, 2857, 0, SOUTH, 8)
        FISHING_SPOT_10514(2288, 2847, 0, SOUTH, 5)
    }
}