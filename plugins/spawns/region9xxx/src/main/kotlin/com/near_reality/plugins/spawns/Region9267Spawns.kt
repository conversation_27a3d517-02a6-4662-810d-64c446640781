package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9267Spawns : NPCSpawnsScript() {
    init {
        DIRE_WOLF(2311, 3271, 0, SOUTH, 12)
        DIRE_WOLF(2317, 3271, 0, SOUTH, 12)
        DIRE_WOLF(2320, 3266, 0, SOUTH, 12)
        DIRE_WOLF(2335, 3276, 0, SOUTH, 12)
        DEADLY_RED_SPIDER(2337, 3311, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2337, 3314, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2338, 3310, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        DIRE_WOLF(2339, 3272, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        DIRE_WOLF(2339, 3279, 0, <PERSON>O<PERSON><PERSON>, 12)
        DEADLY_RED_SPIDER(2341, 3309, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2342, 3311, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2343, 3309, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2344, 3318, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2345, 3310, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2345, 3312, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2350, 3310, 0, SOUTH, 8)
    }
}