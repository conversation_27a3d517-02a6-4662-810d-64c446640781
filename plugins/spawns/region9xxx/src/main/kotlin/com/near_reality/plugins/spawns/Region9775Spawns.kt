package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9775Spawns : NPCSpawnsScript() {
    init {
        UGLUG_NAR(2442, 3049, 0, SOUTH, 3)
        OGRE_GUARD_865(2443, 3038, 0, SOUTH, 2)
        PILG(2443, 3046, 0, SOUTH, 0)
        GRISH(2443, 3051, 0, SOUTH, 5)
        GRUG(2446, 3049, 0, SOUTH, 0)
        OGRE_GUARD_865(2452, 3030, 0, SOUTH, 2)
        OGRE_GUARD(2454, 3047, 0, WEST, 0)
        SKOGRE(2461, 3047, 0, SOUTH, 2)
        ZOGRE_874(2464, 3045, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        ZOGRE_873(2464, 3049, 0, SOUTH, 2)
        SKOGRE(2466, 3052, 0, SOUTH, 2)
        ZOGRE_875(2467, 3049, 0, SOUTH, 2)
        ZOGRE_876(2468, 3046, 0, SOUTH, 2)
        SKOGRE_878(2469, 3039, 0, NORTH_WEST, 2)
        ZOGRE_877(2470, 3055, 0, SOUTH_WEST, 2)
        SKOGRE_879(2480, 3046, 0, SOUTH, 5)
        ZOGRE_867(2482, 3046, 0, SOUTH, 5)
        ZOGRE(2486, 3048, 0, SOUTH, 5)
    }
}