package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9622Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(2370, 9640, 0, SOUTH, 14)
        RAT_2854(2370, 9641, 0, SOUTH, 14)
        BLESSED_GIANT_RAT(2370, 9642, 0, SOUTH, 5)
        SKELETON(2371, 9611, 0, SOUTH, 7)
        SKELETON_78(2372, 9606, 0, SOUTH, 8)
        BLESSED_GIANT_RAT_4535(2372, 9640, 0, SOUTH, 5)
        RAT_2854(2372, 9642, 0, SOUTH, 14)
        RAT_2854(2372, 9643, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        GIANT_BAT(2373, 9630, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GIANT_BAT(2374, 9635, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        SKELETON_71(2375, 9609, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        GOBLIN_3045(2377, 9657, 0, SOUTH, 22)
        RAT_2854(2379, 9630, 0, SOUTH, 14)
        RAT_2854(2379, 9638, 0, SOUTH, 14)
        SKELETON_77(2380, 9609, 0, SOUTH, 7)
        9208(2380, 9659, 0, SOUTH, 5)
        RAT_2854(2381, 9619, 0, SOUTH, 14)
        9207(2381, 9654, 0, SOUTH, 5)
        GOBLIN_3045(2381, 9655, 0, SOUTH, 22)
        9207(2382, 9653, 0, SOUTH, 5)
        SKELETON_80(2383, 9605, 0, SOUTH, 8)
        RAT_2854(2383, 9619, 0, SOUTH, 14)
        RAT_2854(2384, 9619, 0, SOUTH, 14)
        RAT_2854(2384, 9640, 0, SOUTH, 14)
        GOBLIN_3045(2384, 9655, 0, SOUTH, 22)
        9206(2384, 9659, 0, SOUTH, 5)
        RAT_2854(2385, 9624, 0, SOUTH, 14)
        9206(2385, 9657, 0, SOUTH, 5)
        RAT_2854(2386, 9619, 0, SOUTH, 14)
        9205(2387, 9653, 0, SOUTH, 5)
        9204(2389, 9658, 0, SOUTH, 5)
        RAT_2854(2390, 9620, 0, SOUTH, 14)
        BLESSED_GIANT_RAT(2391, 9618, 0, SOUTH, 5)
        GOBLIN_3046(2391, 9655, 0, SOUTH, 5)
        8677(2392, 9653, 0, SOUTH, 5)
        GOBLIN_3045(2392, 9656, 0, SOUTH, 22)
        9203(2392, 9659, 0, SOUTH, 5)
        RAT_2854(2393, 9618, 0, SOUTH, 14)
        RAT_2854(2394, 9639, 0, SOUTH, 14)
        9203(2394, 9658, 0, SOUTH, 5)
        RAT_2854(2395, 9619, 0, SOUTH, 14)
        SKELETON(2396, 9611, 0, SOUTH, 7)
        RAT_2854(2396, 9620, 0, SOUTH, 14)
        GOBLIN_3045(2396, 9658, 0, SOUTH, 22)
        9209(2397, 9603, 0, SOUTH, 5)
        SKELETON_78(2397, 9606, 0, SOUTH, 8)
        BLESSED_GIANT_RAT(2397, 9619, 0, SOUTH, 5)
        GOBLIN_3045(2397, 9655, 0, SOUTH, 22)
        SKELETON_71(2400, 9609, 0, SOUTH, 7)
        RAT_2854(2400, 9620, 0, SOUTH, 14)
        RAT_2854(2402, 9628, 0, SOUTH, 14)
        SKELETON_77(2405, 9609, 0, SOUTH, 7)
        RAT_2854(2405, 9620, 0, SOUTH, 14)
        RAT_2854(2406, 9628, 0, SOUTH, 14)
        RAT_2854(2407, 9620, 0, SOUTH, 14)
        SKELETON_80(2408, 9605, 0, SOUTH, 8)
        RAT_2854(2409, 9629, 0, SOUTH, 14)
        RAT_2854(2412, 9619, 0, SOUTH, 14)
        RAT_2854(2412, 9640, 0, SOUTH, 14)
        GIANT_BAT(2419, 9636, 0, SOUTH, 11)
        RAT_2854(2421, 9632, 0, SOUTH, 14)
        GIANT_BAT(2421, 9634, 0, SOUTH, 11)
        RAT_2854(2422, 9622, 0, SOUTH, 14)
        GIANT_BAT(2422, 9631, 0, SOUTH, 11)
        8977(2423, 9609, 0, SOUTH, 5)
    }
}