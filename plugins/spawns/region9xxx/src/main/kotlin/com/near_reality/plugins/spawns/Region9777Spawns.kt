package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9777Spawns : NPCSpawnsScript() {
    init {
        6403(2439, 3186, 0, SOUTH, 3)
        OBSERVATORY_ASSISTANT(2443, 3189, 0, NORTH_WEST, 0)
        ROD_FISHING_SPOT_1507(2461, 3150, 0, SOUTH, 0)
        ROD_FISHING_SPOT_1507(2462, 3145, 0, SOUTH, 0)
        ROD_FISHING_SPOT_1507(2465, 3156, 0, SOUTH, 0)
        SMELLYTOES(2465, 3183, 0, EAST, 0)
        GREASYCHEEKS(2467, 3183, 0, WEST, 0)
        ROD_FISHING_SPOT_1507(2468, 3157, 0, <PERSON><PERSON><PERSON>H, 0)
        CLOTHEARS(2469, 3158, 0, <PERSON><PERSON>UTH, 0)
        CREAKYKNEES(2470, 3185, 0, <PERSON>OUTH, 0)
        ROD_FISHING_<PERSON>OT_1507(2474, 3153, 0, SOUTH, 0)
        GRIZZLY_BEAR(2477, 3143, 0, SOUTH, 11)
        GRIZZLY_BEAR(2484, 3155, 0, SOUTH, 11)
        TOOL_LEPRECHAUN(2485, 3178, 0, SOUTH, 0)
        GILETH(2488, 3179, 0, SOUTH, 4)
        6404(2437, 3160, 1, SOUTH, 0)
    }
}