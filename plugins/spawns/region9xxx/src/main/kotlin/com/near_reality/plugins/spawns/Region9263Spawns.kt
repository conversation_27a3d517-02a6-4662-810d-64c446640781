package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9263Spawns : NPCSpawnsScript() {
    init {
        SWAMP_TOAD(2332, 3061, 0, SOUTH, 3)
        SWAMP_TOAD(2332, 3063, 0, SOUTH, 3)
        SWAMP_TOAD(2333, 3060, 0, SOUTH, 3)
        SWAMP_TOAD(2334, 3065, 0, SOUTH, 3)
        SWAMP_TOAD(2335, 3067, 0, SOUTH, 3)
        SWAMP_TOAD(2336, 3059, 0, SOUTH, 3)
        SWAMP_TOAD(2336, 3068, 0, SOUTH, 3)
        SWAMP_TOAD(2337, 3067, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2338, 3056, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2338, 3061, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2339, 3066, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2340, 3056, 0, SOUT<PERSON>, 3)
        SWAMP_TOAD(2341, 3062, 0, SOUTH, 3)
        SWAMP_TOAD(2342, 3057, 0, SOUTH, 3)
        SWAMP_TOAD(2342, 3062, 0, SOUTH, 3)
        GRUH(2349, 3064, 0, SOUTH, 4)
    }
}