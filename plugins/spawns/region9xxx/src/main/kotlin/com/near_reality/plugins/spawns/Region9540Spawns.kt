package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9540Spawns : NPCSpawnsScript() {
    init {
        BABY_TANGLEFOOT(2373, 4387, 0, SOUTH, 4)
        BABY_TANGLEFOOT(2373, 4391, 0, SOUTH, 4)
        BABY_TANGLEFOOT_5854(2375, 4381, 0, SOUTH, 4)
        BABY_TANGLEFOOT(2377, 4376, 0, SOUTH, 4)
        BABY_TANGLEFOOT(2377, 4386, 0, SOUTH, 4)
        BABY_TANGLEFOOT(2392, 4379, 0, SOUT<PERSON>, 4)
        BABY_TANGLEFOOT_5854(2394, 4378, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        <PERSON>BY_TANGLEFOOT_5854(2397, 4358, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        6534(2401, 4378, 0, <PERSON>OUTH, 4)
        1023(2412, 4370, 0, <PERSON>OUTH, 0)
        1023(2416, 4376, 0, SOUTH, 0)
        1023(2419, 4374, 0, SOUTH, 0)
        1023(2420, 4377, 0, SOUTH, 0)
        1023(2421, 4370, 0, SOUTH, 0)
    }
}