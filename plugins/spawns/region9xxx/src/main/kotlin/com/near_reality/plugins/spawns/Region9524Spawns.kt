package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9524Spawns : NPCSpawnsScript() {
    init {
        GIANT_SPIDER_3017(2369, 3374, 0, SOUT<PERSON>, 10)
        GIANT_SPIDER_3017(2372, 3379, 0, SOUTH, 10)
        GOBLIN_3045(2373, 3333, 0, SOUTH, 22)
        GIANT_SPIDER_3017(2378, 3366, 0, <PERSON>OUTH, 10)
        WOLF(2379, 3344, 0, <PERSON><PERSON>UTH, 6)
        GIANT_SPIDER_3017(2379, 3378, 0, SOUT<PERSON>, 10)
        GOBLIN_3045(2385, 3339, 0, SO<PERSON><PERSON>, 22)
        GIANT_SPIDER_3017(2387, 3370, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        G<PERSON><PERSON><PERSON><PERSON>Y_BEAR(2387, 3376, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GOBLIN_3045(2394, 3334, 0, <PERSON><PERSON><PERSON><PERSON>, 22)
        GIANT_SPIDER_3017(2394, 3365, 0, SOUT<PERSON>, 10)
        W<PERSON>F(2395, 3340, 0, SOUTH, 6)
        WOLF(2398, 3330, 0, SOUTH, 6)
        GRIZZLY_BEAR(2398, 3366, 0, SOUTH, 11)
        GNOME_6094(2399, 3356, 0, SOUTH, 2)
        GNOME_6095(2400, 3356, 0, SOUTH, 4)
        GNOME_6096(2401, 3357, 0, SOUTH, 2)
        GIANT_SPIDER_3017(2402, 3386, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2405, 3381, 0, SOUTH, 10)
        WOLF(2406, 3329, 0, SOUTH, 6)
        GIANT_SPIDER_3017(2407, 3387, 0, SOUTH, 10)
        GRIZZLY_BEAR(2412, 3378, 0, SOUTH, 11)
        GIANT_SPIDER_3017(2412, 3384, 0, SOUTH, 10)
        GRIZZLY_BEAR(2419, 3372, 0, SOUTH, 11)
    }
}