package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9272Spawns : NPCSpawnsScript() {
    init {
        COPPER_LONGTAIL(2305, 3593, 0, SOUTH, 7)
        COPPER_LONGTAIL(2307, 3596, 0, SOUTH, 7)
        PRICKLY_KEBBIT(2307, 3605, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2307, 3606, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2307, 3620, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2307, 3621, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2308, 3642, 0, <PERSON>OUT<PERSON>, 2)
        COPPER_LONGTAIL(2309, 3593, 0, <PERSON>O<PERSON><PERSON>, 7)
        COPPER_LONGTAIL(2310, 3587, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        PRICKLY_KEBBIT(2310, 3642, 0, <PERSON>OUT<PERSON>, 2)
        RUBY_HARVEST(2311, 3611, 0, SOUTH, 6)
        COPPER_LONGTAIL(2313, 3584, 0, SOUTH, 7)
        CHINCHOMPA(2314, 3596, 0, SOUTH, 7)
        COPPER_LONGTAIL(2315, 3587, 0, SOUTH, 7)
        RUBY_HARVEST(2315, 3613, 0, SOUTH, 6)
        RUBY_HARVEST(2316, 3603, 0, SOUTH, 6)
        CHINCHOMPA(2316, 3606, 0, SOUTH, 7)
        PRICKLY_KEBBIT(2319, 3595, 0, SOUTH, 2)
        RUBY_HARVEST(2319, 3597, 0, SOUTH, 6)
        PRICKLY_KEBBIT(2320, 3595, 0, SOUTH, 2)
        CHINCHOMPA(2321, 3599, 0, SOUTH, 7)
        RUBY_HARVEST(2321, 3605, 0, SOUTH, 6)
        CHINCHOMPA(2321, 3611, 0, SOUTH, 7)
        PRICKLY_KEBBIT(2321, 3643, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2321, 3644, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2323, 3614, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2323, 3628, 0, SOUTH, 2)
        RUBY_HARVEST(2324, 3594, 0, SOUTH, 6)
        PRICKLY_KEBBIT(2324, 3614, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2324, 3628, 0, SOUTH, 2)
        RUBY_HARVEST(2325, 3600, 0, SOUTH, 6)
        PRICKLY_KEBBIT(2327, 3635, 0, SOUTH, 2)
        RUBY_HARVEST(2328, 3598, 0, SOUTH, 6)
        PRICKLY_KEBBIT(2328, 3635, 0, SOUTH, 2)
        CHINCHOMPA(2330, 3622, 0, SOUTH, 7)
        CHINCHOMPA(2332, 3626, 0, SOUTH, 7)
        RUBY_HARVEST(2334, 3586, 0, SOUTH, 6)
        CHINCHOMPA(2334, 3591, 0, SOUTH, 7)
        PRICKLY_KEBBIT(2336, 3631, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2336, 3632, 0, SOUTH, 2)
        RUBY_HARVEST(2338, 3591, 0, SOUTH, 6)
        CHINCHOMPA(2339, 3593, 0, SOUTH, 7)
        CHINCHOMPA(2339, 3615, 0, SOUTH, 7)
        COPPER_LONGTAIL(2341, 3599, 0, SOUTH, 7)
        COPPER_LONGTAIL(2341, 3604, 0, SOUTH, 7)
        CHINCHOMPA(2341, 3618, 0, SOUTH, 7)
        CHINCHOMPA(2342, 3590, 0, SOUTH, 7)
        PRICKLY_KEBBIT(2342, 3641, 0, SOUTH, 2)
        PRICKLY_KEBBIT(2343, 3641, 0, SOUTH, 2)
        COPPER_LONGTAIL(2345, 3601, 0, SOUTH, 7)
        COPPER_LONGTAIL(2352, 3584, 0, SOUTH, 7)
        COPPER_LONGTAIL(2354, 3587, 0, SOUTH, 7)
        9392(2354, 3608, 0, SOUTH, 0)
        COPPER_LONGTAIL(2357, 3584, 0, SOUTH, 7)
        KATHY_CORKAT_4299(2357, 3640, 0, SOUTH, 2)
        COPPER_LONGTAIL(2360, 3591, 0, SOUTH, 7)
        COPPER_LONGTAIL(2362, 3588, 0, SOUTH, 7)
        COPPER_LONGTAIL(2364, 3591, 0, SOUTH, 7)
    }
}