package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9565Spawns : NPCSpawnsScript() {
    init {
        9807(2398, 5981, 0, SOUTH, 5)
        9809(2398, 5985, 0, SOUTH, 5)
        9811(2400, 5983, 0, SOUTH, 5)
        9806(2400, 5986, 0, SOUTH, 5)
        9810(2402, 5981, 0, SOUTH, 5)
        9808(2402, 5985, 0, SOUTH, 5)
    }
}