package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9046Spawns : NPCSpawnsScript() {
    init {
        MOUSE_5128(2267, 5516, 0, SOUTH, 5)
        MOUSE_5128(2278, 5533, 0, SOUTH, 5)
        MOUSE_5127(2278, 5549, 0, SOUTH, 5)
        MOUSE_5127(2267, 5516, 1, SOUTH, 5)
        MOUSE_5128(2277, 5525, 1, SOUTH, 5)
        MOUSE_5127(2278, 5545, 1, SOUTH, 5)
        MOUSE_5128(2270, 5516, 2, SOUTH, 5)
        MOUSE_5127(2277, 5526, 2, SOUTH, 5)
        MOUSE_5128(2279, 5541, 2, SOUTH, 5)
        MOUSE_5127(2279, 5554, 2, <PERSON>OUTH, 5)
        MOUSE_5128(2264, 5516, 3, SOUTH, 5)
        MOUSE_5127(2278, 5523, 3, SOUTH, 5)
        MOUSE_5127(2278, 5544, 3, SOUTH, 5)
    }
}