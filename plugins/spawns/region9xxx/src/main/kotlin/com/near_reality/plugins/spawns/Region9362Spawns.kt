package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9362Spawns : NPCSpawnsScript() {
    init {
        GOBLIN_3045(2305, 9385, 0, SOUT<PERSON>, 22)
        GOBLIN_3045(2307, 9359, 0, SOUT<PERSON>, 22)
        GOBLIN_3045(2307, 9403, 0, SOUTH, 22)
        GOBLIN_3045(2310, 9396, 0, SOUTH, 22)
        GOBLIN_3028(2316, 9392, 0, SOUTH, 34)
        GOBLIN_3045(2317, 9371, 0, <PERSON>O<PERSON><PERSON>, 22)
        GOBLIN_3045(2317, 9383, 0, SOUTH, 22)
        GOBLIN_3028(2319, 9367, 0, <PERSON><PERSON><PERSON><PERSON>, 34)
        GOBLIN_3028(2321, 9402, 0, <PERSON><PERSON><PERSON><PERSON>, 34)
        GOBLIN_3028(2322, 9387, 0, <PERSON><PERSON><PERSON><PERSON>, 34)
        GOBLIN_5376(2323, 9377, 0, <PERSON><PERSON><PERSON><PERSON>, 27)
        GOBLIN_3045(2324, 9404, 0, SOUTH, 22)
        GOBL<PERSON>_3028(2325, 9360, 0, SOUTH, 34)
        SLEEPING_GUARD(2327, 9394, 0, NORTH, 0)
        GOBLIN_3028(2333, 9346, 0, SOUTH, 34)
        <PERSON><PERSON><PERSON>_3045(2333, 9366, 0, SOUTH, 22)
        GO<PERSON>IN_3028(2335, 9382, 0, SOUTH, 34)
        GOBLIN_3045(2335, 9393, 0, SOUTH, 22)
        GOBLIN_3028(2337, 9387, 0, SOUTH, 34)
        RAT_2854(2339, 9356, 0, SOUTH, 14)
        GOBLIN_3045(2339, 9403, 0, SOUTH, 22)
        GOBLIN_3045(2342, 9347, 0, SOUTH, 22)
        GOBLIN_3028(2343, 9360, 0, SOUTH, 34)
        GOBLIN_3028(2344, 9369, 0, SOUTH, 34)
        GOBLIN_3028(2348, 9390, 0, SOUTH, 34)
        GOBLIN_3028(2349, 9380, 0, SOUTH, 34)
        GOBLIN_3028(2349, 9402, 0, SOUTH, 34)
        GOBLIN_5377(2351, 9358, 0, SOUTH, 15)
        GOBLIN_3045(2351, 9359, 0, SOUTH, 22)
        RAT_2854(2354, 9390, 0, SOUTH, 14)
        GOBLIN_3028(2359, 9345, 0, SOUTH, 34)
        GOBLIN_3028(2359, 9359, 0, SOUTH, 34)
        GOBLIN_3028(2359, 9374, 0, SOUTH, 34)
        GOBLIN_3028(2359, 9382, 0, SOUTH, 34)
        GOBLIN_3045(2359, 9392, 0, SOUTH, 22)
        RAT_2854(2361, 9403, 0, SOUTH, 14)
        RAT_2854(2362, 9347, 0, SOUTH, 14)
        GOBLIN_3028(2363, 9403, 0, SOUTH, 34)
        WAGCHIN(2364, 9398, 0, SOUTH, 0)
        NAGHEAD(2364, 9399, 0, NORTH, 0)
    }
}