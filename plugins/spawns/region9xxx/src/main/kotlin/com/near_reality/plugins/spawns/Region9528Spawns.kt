package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9528Spawns : NPCSpawnsScript() {
    init {
        DARK_KEBBIT(2368, 3589, 0, SOUTH, 2)
        DARK_KEBBIT(2368, 3594, 0, SOUTH, 2)
        SPOTTED_KEBBIT(2370, 3587, 0, SOUTH, 2)
        SPOTTED_KEBBIT(2370, 3591, 0, SOUTH, 2)
        SPOTTED_KEBBIT(2372, 3584, 0, SOUTH, 2)
        MATTHIAS(2376, 3606, 0, SOUTH, 2)
        SPOTTED_KEBBIT(2379, 3585, 0, SOUTH, 2)
        SPOTTED_KEBBIT(2382, 3584, 0, <PERSON>OUT<PERSON>, 2)
        SPOTTED_KEBBIT(2384, 3586, 0, SOUTH, 2)
        DARK_KEBBIT(2388, 3590, 0, SOUTH, 2)
        DARK_KEBBIT(2389, 3598, 0, SOUTH, 2)
        DASHING_KEBBIT(2390, 3588, 0, SOUTH, 3)
        SPOTTED_KEBBIT(2391, 3592, 0, SOUTH, 2)
    }
}