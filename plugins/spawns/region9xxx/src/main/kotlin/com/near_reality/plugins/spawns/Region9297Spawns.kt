package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9297Spawns : NPCSpawnsScript() {
    init {
        GHOST_2531(2305, 5233, 0, SOUTH, 8)
        GHOST_2531(2305, 5241, 0, SOUTH, 8)
        GHOST_2531(2306, 5229, 0, SOUTH, 8)
        GHOST_2531(2306, 5243, 0, SOUTH, 8)
        SKELETON_2525(2309, 5187, 0, SOUTH, 5)
        GHOST_2531(2309, 5246, 0, SOUTH, 8)
        SKELETON_2526(2310, 5189, 0, SOUTH, 5)
        SKELETON_2524(2311, 5187, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SKELETON_2525(2311, 5191, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKELETON_2524(2311, 5193, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GHOST_2531(2311, 5237, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON_2526(2312, 5188, 0, SOUTH, 5)
        GHOST_2531(2314, 5237, 0, SOUTH, 8)
        GHOST_2531(2314, 5244, 0, SOUTH, 8)
        GHOST_2528(2315, 5226, 0, SOUTH, 7)
        ANKOU_2516(2315, 5229, 0, SOUTH, 9)
        GHOST_2531(2315, 5236, 0, SOUTH, 8)
        GHOST_2531(2316, 5227, 0, SOUTH, 8)
        ANKOU_2516(2317, 5226, 0, SOUTH, 9)
        GHOST_2529(2318, 5224, 0, SOUTH, 7)
        ANKOU_2516(2318, 5230, 0, SOUTH, 9)
        ANKOU_2516(2320, 5224, 0, SOUTH, 9)
        GHOST_2530(2320, 5226, 0, SOUTH, 7)
        GHOST_2530(2320, 5229, 0, SOUTH, 7)
        GHOST_2530(2321, 5225, 0, SOUTH, 7)
        ANKOU_2516(2321, 5232, 0, SOUTH, 9)
        SKELETON_2523(2322, 5199, 0, SOUTH, 7)
        ANKOU_2516(2322, 5222, 0, SOUTH, 9)
        ANKOU_2516(2322, 5227, 0, SOUTH, 9)
        ANKOU_2516(2322, 5229, 0, SOUTH, 9)
        GHOST_2529(2322, 5230, 0, SOUTH, 7)
        GHOST_2531(2322, 5235, 0, SOUTH, 8)
        ANKOU_2516(2323, 5224, 0, SOUTH, 9)
        ANKOU_2515(2324, 5198, 0, SOUTH, 7)
        SKELETON_2523(2324, 5206, 0, SOUTH, 7)
        GHOST_2529(2324, 5224, 0, SOUTH, 7)
        ANKOU_2516(2324, 5230, 0, SOUTH, 9)
        ANKOU_2516(2324, 5236, 0, SOUTH, 9)
        ANKOU_2515(2325, 5197, 0, SOUTH, 7)
        GHOST_2531(2325, 5228, 0, SOUTH, 8)
        GHOST_2528(2325, 5232, 0, SOUTH, 7)
        SKELETON_2520(2326, 5199, 0, SOUTH, 6)
        ANKOU_2515(2326, 5201, 0, SOUTH, 7)
        ANKOU_2515(2326, 5205, 0, SOUTH, 7)
        ANKOU_2515(2326, 5206, 0, SOUTH, 7)
        SKELETON_2520(2326, 5208, 0, SOUTH, 6)
        GHOST_2528(2326, 5226, 0, SOUTH, 7)
        ANKOU_2515(2327, 5199, 0, SOUTH, 7)
        ANKOU_2515(2327, 5202, 0, SOUTH, 7)
        ANKOU_2516(2327, 5227, 0, SOUTH, 9)
        ANKOU_2515(2328, 5197, 0, SOUTH, 7)
        SKELETON_2521(2328, 5205, 0, SOUTH, 6)
        SKELETON_2521(2329, 5200, 0, SOUTH, 6)
        ANKOU_2515(2329, 5203, 0, SOUTH, 7)
        ANKOU_2515(2330, 5195, 0, SOUTH, 7)
        ANKOU_2515(2331, 5202, 0, SOUTH, 7)
        SKELETON_2520(2332, 5202, 0, SOUTH, 6)
        SKELETON_2523(2333, 5202, 0, SOUTH, 7)
        SOLZTUN(2338, 5213, 0, SOUTH, 2)
        SKELETON_2522(2346, 5196, 0, SOUTH, 6)
        GHOST_2528(2348, 5194, 0, SOUTH, 7)
        SKELETON_2521(2348, 5197, 0, SOUTH, 6)
        SKELETON_2523(2349, 5197, 0, SOUTH, 7)
        SKELETON_2520(2350, 5200, 0, SOUTH, 6)
        GHOST_2527(2352, 5189, 0, SOUTH, 4)
        SKELETON_2520(2352, 5195, 0, SOUTH, 6)
        GHOST_2529(2352, 5200, 0, SOUTH, 7)
        SKELETON_2520(2353, 5192, 0, SOUTH, 6)
        GHOST_2527(2353, 5197, 0, SOUTH, 4)
        SKELETON_2521(2353, 5203, 0, SOUTH, 6)
        SKELETON_2523(2354, 5189, 0, SOUTH, 7)
        SKELETON_2521(2354, 5191, 0, SOUTH, 6)
        GHOST_2529(2354, 5194, 0, SOUTH, 7)
        GHOST_2530(2354, 5200, 0, SOUTH, 7)
        GHOST_2528(2354, 5202, 0, SOUTH, 7)
        SKELETON_2522(2355, 5195, 0, SOUTH, 6)
        SKELETON_2520(2355, 5198, 0, SOUTH, 6)
        ANKOU(2355, 5241, 0, SOUTH, 4)
        SKELETON_2522(2356, 5190, 0, SOUTH, 6)
        GHOST_2530(2356, 5193, 0, SOUTH, 7)
        GHOST_2527(2356, 5204, 0, SOUTH, 4)
        SHADE_6740(2356, 5216, 0, SOUTH, 2)
        GHOST_2527(2357, 5194, 0, SOUTH, 4)
        SHADE_6740(2357, 5213, 0, SOUTH, 2)
        ANKOU(2357, 5240, 0, SOUTH, 4)
        ANKOU(2358, 5243, 0, SOUTH, 4)
        ANKOU(2358, 5245, 0, SOUTH, 4)
        ANKOU(2359, 5239, 0, SOUTH, 4)
        ANKOU(2359, 5241, 0, SOUTH, 4)
        ANKOU(2361, 5240, 0, SOUTH, 4)
        ANKOU(2361, 5242, 0, SOUTH, 4)
        ZOMBIE_6741(2363, 5209, 0, SOUTH, 2)
        SHADE_6740(2363, 5212, 0, SOUTH, 2)
        ANKOU(2363, 5240, 0, SOUTH, 4)
        SHADE_6740(2365, 5216, 0, SOUTH, 2)
    }
}