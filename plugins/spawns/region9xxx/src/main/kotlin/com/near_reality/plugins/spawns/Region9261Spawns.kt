package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9261Spawns : NPCSpawnsScript() {
    init {
        BIRD_10541(2309, 2903, 0, SOUTH, 5)
        SQUIRREL(2313, 2891, 0, SOUTH, 8)
        BIRD_10541(2318, 2941, 0, SOUTH, 5)
        10543(2320, 2925, 0, SOUTH, 5)
        SQUIRREL_1418(2322, 2935, 0, SOUTH, 10)
        BUTTERFLY_238(2326, 2894, 0, SOUTH, 0)
        SQUIRREL_1417(2331, 2911, 0, SOUTH, 9)
        BUTTERFLY_238(2333, 2920, 0, SOUTH, 0)
    }
}