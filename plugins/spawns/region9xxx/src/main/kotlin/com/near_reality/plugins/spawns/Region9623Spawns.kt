package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9623Spawns : NPCSpawnsScript() {
    init {
        OGRE_2095(2387, 9685, 0, SOUTH, 2)
        OGRE_2095(2389, 9683, 0, SOUTH, 2)
        ZOMBIE_27(2394, 9707, 0, SOUTH, 5)
        BLESSED_SPIDER(2395, 9683, 0, SOUTH, 5)
        ZOMBIE_49(2396, 9710, 0, SOUTH, 4)
        BLESSED_SPIDER(2397, 9684, 0, SOUTH, 5)
        ZOMBIE_51(2397, 9702, 0, SOUTH, 4)
        ZOMBIE_29(2398, 9704, 0, <PERSON>OUT<PERSON>, 5)
        ZOMBIE_50(2398, 9707, 0, <PERSON>OUTH, 3)
        ZOMBIE_51(2399, 9712, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BLESSED_SPIDER(2400, 9682, 0, <PERSON>OUTH, 5)
        ZOMBIE_31(2400, 9701, 0, SOUTH, 5)
        ZOMBIE_50(2401, 9714, 0, SOUTH, 3)
        BLESSED_SPIDER(2402, 9680, 0, SOUTH, 5)
        ZOMBIE(2405, 9713, 0, SOUTH, 5)
        ZOMBIE_51(2406, 9709, 0, SOUTH, 4)
        ZOMBIE_49(2407, 9702, 0, SOUTH, 4)
        ZOMBIE_49(2408, 9712, 0, SOUTH, 4)
        ZOMBIE_50(2409, 9704, 0, SOUTH, 3)
        ZOMBIE_28(2410, 9707, 0, SOUTH, 4)
        9211(2422, 9718, 0, SOUTH, 5)
        9210(2424, 9721, 0, SOUTH, 5)
        9212(2426, 9718, 0, SOUTH, 5)
    }
}