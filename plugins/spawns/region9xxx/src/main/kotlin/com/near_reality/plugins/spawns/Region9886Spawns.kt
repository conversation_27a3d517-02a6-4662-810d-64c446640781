package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9886Spawns : NPCSpawnsScript() {
    init {
        ROCKS(2442, 10159, 0, SOUTH, 0)
        DAGANNOTH_3185(2443, 10150, 0, SOUTH, 40)
        ROCKS_103(2443, 10158, 0, SOUTH, 0)
        ROCKS_103(2443, 10159, 0, SOUTH, 0)
        EGG_5932(2444, 10137, 0, SOUTH, 0)
        DAGANNOTH_3185(2444, 10141, 0, SOUTH, 40)
        DAGANNOTH_3185(2445, 10154, 0, SOUTH, 40)
        DAGANNOTH_3185(2447, 10140, 0, <PERSON><PERSON><PERSON><PERSON>, 40)
        EGG_5932(2447, 10164, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        EGG_5932(2448, 10166, 0, <PERSON><PERSON><PERSON>H, 0)
        ROCKS(2449, 10160, 0, SOUTH, 0)
        ROCKS_103(2449, 10164, 0, SOUTH, 0)
        EGG_5932(2449, 10167, 0, SOUTH, 0)
        EGG_5932(2451, 10134, 0, SOUTH, 0)
        DAGANNOTH_3185(2451, 10146, 0, SOUTH, 40)
        DAGANNOTH_3185(2451, 10161, 0, SOUTH, 40)
        ROCKS(2451, 10165, 0, SOUTH, 0)
        EGG_5932(2452, 10133, 0, SOUTH, 0)
        EGG_5932(2452, 10136, 0, SOUTH, 0)
        EGG_5932(2454, 10126, 0, SOUTH, 0)
        EGG_5932(2454, 10129, 0, SOUTH, 0)
        EGG_5932(2454, 10132, 0, SOUTH, 0)
        DAGANNOTH_3185(2454, 10145, 0, SOUTH, 40)
        DAGANNOTH_3185(2454, 10149, 0, SOUTH, 40)
        ROCKS_103(2454, 10159, 0, SOUTH, 0)
        DAGANNOTH_3185(2455, 10155, 0, SOUTH, 40)
        ROCKS(2455, 10164, 0, SOUTH, 0)
        DAGANNOTH_3185(2456, 10136, 0, SOUTH, 40)
        ROCKS(2456, 10161, 0, SOUTH, 0)
        DAGANNOTH_3185(2457, 10141, 0, SOUTH, 40)
        EGG_5932(2458, 10147, 0, SOUTH, 0)
        EGG_5932(2458, 10148, 0, SOUTH, 0)
        DAGANNOTH_3185(2458, 10156, 0, SOUTH, 40)
        ROCKS_103(2458, 10160, 0, SOUTH, 0)
        EGG_5932(2459, 10127, 0, SOUTH, 0)
        DAGANNOTH_3185(2459, 10136, 0, SOUTH, 40)
        ROCKS(2459, 10166, 0, SOUTH, 0)
        EGG_5932(2459, 10169, 0, SOUTH, 0)
        DAGANNOTH_3185(2460, 10131, 0, SOUTH, 40)
        ROCKS(2460, 10153, 0, SOUTH, 0)
        ROCKS_103(2460, 10155, 0, SOUTH, 0)
        EGG_5932(2460, 10169, 0, SOUTH, 0)
        DAGANNOTH_3185(2461, 10138, 0, SOUTH, 40)
        ROCKS_103(2461, 10154, 0, SOUTH, 0)
        DAGANNOTH_3185(2461, 10163, 0, SOUTH, 40)
        EGG_5932(2462, 10128, 0, SOUTH, 0)
        DAGANNOTH_3185(2463, 10134, 0, SOUTH, 40)
        DAGANNOTH_3185(2463, 10158, 0, SOUTH, 40)
        DAGANNOTH_3185(2464, 10131, 0, SOUTH, 40)
        DAGANNOTH_3185(2464, 10161, 0, SOUTH, 40)
        ROCKS_103(2464, 10165, 0, SOUTH, 0)
        ROCKS_103(2464, 10167, 0, SOUTH, 0)
        EGG_5932(2465, 10125, 0, SOUTH, 0)
        EGG_5932(2466, 10141, 0, SOUTH, 0)
        DAGANNOTH_3185(2466, 10164, 0, SOUTH, 40)
        DAGANNOTH_3185(2467, 10137, 0, SOUTH, 40)
        EGG_5932(2467, 10141, 0, SOUTH, 0)
        DAGANNOTH_3185(2468, 10133, 0, SOUTH, 40)
        DAGANNOTH_3185(2468, 10158, 0, SOUTH, 40)
        EGG_5932(2469, 10125, 0, SOUTH, 0)
        DAGANNOTH_3185(2469, 10130, 0, SOUTH, 40)
        DAGANNOTH_3185(2470, 10136, 0, SOUTH, 40)
        DAGANNOTH_3185(2470, 10161, 0, SOUTH, 40)
        ROCKS_103(2470, 10164, 0, SOUTH, 0)
        DAGANNOTH_3185(2471, 10138, 0, SOUTH, 40)
        DAGANNOTH_3185(2471, 10155, 0, SOUTH, 40)
        DAGANNOTH_3185(2472, 10134, 0, SOUTH, 40)
        ROCKS_103(2472, 10162, 0, SOUTH, 0)
        DAGANNOTH_3185(2473, 10129, 0, SOUTH, 40)
        DAGANNOTH_3185(2473, 10141, 0, SOUTH, 40)
        DAGANNOTH_3185(2474, 10154, 0, SOUTH, 40)
        EGG_5932(2474, 10159, 0, SOUTH, 0)
        EGG_5932(2475, 10126, 0, SOUTH, 0)
        DAGANNOTH_3185(2476, 10143, 0, SOUTH, 40)
        DAGANNOTH_3185(2476, 10148, 0, SOUTH, 40)
        DAGANNOTH_3185(2476, 10156, 0, SOUTH, 40)
        EGG_5932(2477, 10158, 0, SOUTH, 0)
        DAGANNOTH_3185(2478, 10141, 0, SOUTH, 40)
        DAGANNOTH_3185(2478, 10152, 0, SOUTH, 40)
        EGG_5932(2478, 10158, 0, SOUTH, 0)
        DAGANNOTH_3185(2479, 10145, 0, SOUTH, 40)
        DAGANNOTH_3185(2480, 10148, 0, SOUTH, 40)
        EGG_5932(2481, 10125, 0, SOUTH, 0)
        DAGANNOTH_3185(2482, 10127, 0, SOUTH, 40)
        DAGANNOTH_3185(2482, 10143, 0, SOUTH, 40)
        DAGANNOTH_3185(2482, 10154, 0, SOUTH, 40)
        DAGANNOTH_3185(2482, 10157, 0, SOUTH, 40)
        EGG_5932(2485, 10123, 0, SOUTH, 0)
        DAGANNOTH_3185(2485, 10126, 0, SOUTH, 40)
        DAGANNOTH_3185(2485, 10153, 0, SOUTH, 40)
        DAGANNOTH_3185(2485, 10158, 0, SOUTH, 40)
        DAGANNOTH_3185(2485, 10163, 0, SOUTH, 40)
        EGG_5932(2485, 10168, 0, SOUTH, 0)
        DAGANNOTH_3185(2486, 10141, 0, SOUTH, 40)
        EGG_5932(2487, 10124, 0, SOUTH, 0)
        DAGANNOTH_3185(2487, 10131, 0, SOUTH, 40)
        DAGANNOTH_3185(2487, 10146, 0, SOUTH, 40)
        DAGANNOTH_3185(2488, 10137, 0, SOUTH, 40)
        DAGANNOTH_3185(2488, 10155, 0, SOUTH, 40)
        DAGANNOTH_3185(2488, 10162, 0, SOUTH, 40)
        EGG_5932(2489, 10144, 0, SOUTH, 0)
        EGG_5932(2492, 10139, 0, SOUTH, 0)
        EGG_5932(2492, 10155, 0, SOUTH, 0)
    }
}