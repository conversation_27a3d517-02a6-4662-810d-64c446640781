package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9285Spawns : NPCSpawnsScript() {
    init {
        5425(2333, 4450, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        2966(2333, 4457, 0, <PERSON>OUT<PERSON>, 5)
        2967(2334, 4452, 0, SOUTH, 5)
        2964(2340, 4453, 0, SOUTH, 5)
        2967(2343, 4442, 0, <PERSON><PERSON>UTH, 5)
        5425(2345, 4432, 0, <PERSON><PERSON>UT<PERSON>, 5)
        2965(2346, 4450, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        5425(2347, 4444, 0, <PERSON>OUTH, 5)
        1859(2348, 4433, 0, <PERSON>OUT<PERSON>, 5)
        1858(2349, 4435, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        2971(2352, 4457, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        2970(2353, 4446, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        2965(2354, 4439, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        2969(2355, 4456, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        2966(2357, 4437, 0, S<PERSON><PERSON><PERSON>, 5)
        2964(2357, 4451, 0, SOUTH, 5)
    }
}