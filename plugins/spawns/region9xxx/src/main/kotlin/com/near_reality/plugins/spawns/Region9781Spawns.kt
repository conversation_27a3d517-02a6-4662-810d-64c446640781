package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9781Spawns : NPCSpawnsScript() {
    init {
        490(2432, 3423, 0, EAST, 0)
        GNOME_WOMAN(2434, 3436, 0, SOUTH, 5)
        TERRORBIRD(2436, 3434, 0, SOUTH, 9)
        BUTTERFLY(2437, 3425, 0, SOUTH, 7)
        MOUNTED_TERRORBIRD_GNOME(2437, 3442, 0, SOUTH, 11)
        GNOME_WOMAN(2437, 3451, 0, SOUTH, 5)
        PRISSY_SCILLA(2438, 3418, 0, SOUTH, 5)
        BUTTERFLY(2438, 3422, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        GNOME_WOMAN(2438, 3427, 0, <PERSON>O<PERSON><PERSON>, 5)
        TOOL_LEPRECHAUN(2439, 3414, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        GNOME_WOMAN_6087(2439, 3433, 0, <PERSON><PERSON><PERSON>H, 5)
        GNOME_WOMAN(2441, 3411, 0, SOUTH, 5)
        GNOME_WOMAN_6087(2441, 3449, 0, SOUTH, 5)
        GNOME_WOMAN_6087(2442, 3428, 0, SOUTH, 5)
        BUTTERFLY_235(2446, 3396, 0, SOUTH, 7)
        GNOME_WOMAN_6087(2446, 3403, 0, SOUTH, 5)
        MOUNTED_TERRORBIRD_GNOME(2449, 3421, 0, SOUTH, 11)
        GNOME_WOMAN_6087(2450, 3416, 0, SOUTH, 5)
        BUTTERFLY(2450, 3419, 0, SOUTH, 7)
        GNOME_GUARD_6081(2451, 3414, 0, SOUTH, 5)
        GNOME_6095(2456, 3425, 0, SOUTH, 4)
        MOUNTED_TERRORBIRD_GNOME(2457, 3394, 0, SOUTH, 11)
        GNOME_GUARD_6082(2459, 3392, 0, SOUTH, 5)
        6171(2459, 3393, 0, SOUTH, 7)
        6171(2459, 3394, 0, SOUTH, 7)
        GNOME_GUARD_6081(2459, 3395, 0, SOUTH, 5)
        GNOME_6095(2459, 3421, 0, SOUTH, 4)
        GNOME_GUARD_6081(2459, 3438, 0, SOUTH, 5)
        GNOME_GUARD_6082(2461, 3422, 0, SOUTH, 5)
        GNOME_GUARD_6082(2462, 3392, 0, SOUTH, 5)
        GNOME_GUARD_6081(2462, 3395, 0, SOUTH, 5)
        GNOME_6095(2462, 3431, 0, SOUTH, 4)
        6171(2463, 3393, 0, SOUTH, 7)
        6171(2463, 3394, 0, SOUTH, 7)
        MOUNTED_TERRORBIRD_GNOME(2464, 3394, 0, SOUTH, 11)
        GNOME_WOMAN(2466, 3449, 0, SOUTH, 5)
        GNOME_WOMAN_6087(2468, 3441, 0, SOUTH, 5)
        GNOME_TRAINER(2469, 3423, 0, SOUTH, 3)
        GNOME_TRAINER(2469, 3434, 0, SOUTH, 3)
        BUTTERFLY_235(2470, 3397, 0, SOUTH, 7)
        GNOME_WOMAN(2470, 3399, 0, SOUTH, 5)
        GNOME_TRAINER(2470, 3426, 0, SOUTH, 3)
        GNOME_WOMAN(2472, 3400, 0, SOUTH, 5)
        GNOME_WOMAN(2473, 3412, 0, SOUTH, 5)
        BOLONGO(2474, 3446, 0, SOUTH, 4)
        GNOME_WOMAN(2476, 3454, 0, SOUTH, 5)
        GNOME_TRAINER(2477, 3426, 0, SOUTH, 3)
        TOOL_LEPRECHAUN(2478, 3444, 0, SOUTH, 0)
        BUTTERFLY_238(2479, 3396, 0, SOUTH, 0)
        GNOME_TRAINER(2479, 3427, 0, SOUTH, 3)
        6171(2479, 3428, 0, SOUTH, 7)
        MOUNTED_TERRORBIRD_GNOME(2480, 3406, 0, SOUTH, 11)
        GNOME_WOMAN_6087(2480, 3408, 0, SOUTH, 5)
        GNOME_WOMAN(2482, 3397, 0, SOUTH, 5)
        GNOME_TRAINER(2482, 3422, 0, SOUTH, 3)
        GNOME_WOMAN(2489, 3401, 0, SOUTH, 5)
        GNOME_TRAINER(2489, 3423, 0, SOUTH, 3)
        GNOME_TRAINER(2489, 3427, 0, SOUTH, 3)
        GNOME_TRAINER(2490, 3435, 0, SOUTH, 3)
        FROONO(2435, 3403, 1, SOUTH, 2)
        6563(2443, 3424, 1, EAST, 0)
        GNOME_BANKER(2443, 3425, 1, EAST, 0)
        6171(2445, 3416, 1, SOUTH, 7)
        GNOME_GUARD_6081(2445, 3429, 1, SOUTH, 5)
        6171(2445, 3433, 1, SOUTH, 7)
        GUARD_VEMMELDO(2446, 3418, 1, SOUTH, 2)
        GNOME_BANKER(2448, 3424, 1, WEST, 0)
        GNOME_BANKER(2448, 3427, 1, WEST, 0)
        GNOME_TROOP_4974(2458, 3417, 1, SOUTH, 4)
        GNOME_TROOP_4974(2460, 3417, 1, SOUTH, 4)
        GNOME_TRAINER(2475, 3422, 1, SOUTH, 3)
        GNOME_WOMAN(2479, 3407, 1, SOUTH, 5)
        GNOME_WOMAN_6087(2480, 3406, 1, SOUTH, 5)
        GNOME_WOMAN_6087(2486, 3400, 1, SOUTH, 5)
        ERMIN(2488, 3409, 1, EAST, 0)
        GNOME_TRAINER(2475, 3421, 2, SOUTH, 3)
    }
}