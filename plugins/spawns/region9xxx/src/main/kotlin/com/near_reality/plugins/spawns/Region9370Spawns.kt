package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9370Spawns : NPCSpawnsScript() {
    init {
        3104(2311, 9857, 0, SOUTH, 2)
        3104(2314, 9862, 0, SOUTH, 2)
        3104(2315, 9857, 0, SOUTH, 2)
        3104(2317, 9867, 0, SOUTH, 2)
        3104(2320, 9856, 0, SOUTH, 2)
        3104(2320, 9859, 0, SOUTH, 2)
        3104(2323, 9863, 0, SOUTH, 2)
        3104(2324, 9856, 0, SOUTH, 2)
        3104(2324, 9874, 0, SOUTH, 2)
        3104(2325, 9859, 0, SOUTH, 2)
        3104(2325, 9864, 0, SOUTH, 2)
        3104(2325, 9867, 0, SOUTH, 2)
        3104(2328, 9871, 0, SOUTH, 2)
        3104(2329, 9856, 0, SO<PERSON><PERSON>, 2)
        <PERSON>IDER_4561(2329, 9898, 0, SOUTH, 5)
        3104(2330, 9859, 0, SOUTH, 2)
        SPIDER_4561(2330, 9914, 0, SOUTH, 5)
        3104(2331, 9857, 0, SOUTH, 2)
        SPIDER_4561(2332, 9894, 0, SOUTH, 5)
        3104(2333, 9858, 0, SOUTH, 2)
        SPIDER_4561(2334, 9878, 0, SOUTH, 5)
        SPIDER_4561(2334, 9887, 0, SOUTH, 5)
        SPIDER_4561(2334, 9900, 0, SOUTH, 5)
        3104(2335, 9868, 0, SOUTH, 2)
        SPIDER_4561(2336, 9893, 0, SOUTH, 5)
        3104(2337, 9856, 0, SOUTH, 2)
        3104(2337, 9872, 0, SOUTH, 2)
        SPIDER_4561(2337, 9888, 0, SOUTH, 5)
        SPIDER_4561(2337, 9898, 0, SOUTH, 5)
        3104(2338, 9862, 0, SOUTH, 2)
        SPIDER_4561(2338, 9906, 0, SOUTH, 5)
        SPIDER_4561(2339, 9875, 0, SOUTH, 5)
        SPIDER_4561(2339, 9881, 0, SOUTH, 5)
        SPIDER_4561(2339, 9893, 0, SOUTH, 5)
        SPIDER_4561(2339, 9914, 0, SOUTH, 5)
        3104(2340, 9860, 0, SOUTH, 2)
        SPIDER_4561(2341, 9897, 0, SOUTH, 5)
        3104(2342, 9857, 0, SOUTH, 2)
        3104(2342, 9862, 0, SOUTH, 2)
        3104(2342, 9866, 0, SOUTH, 2)
        SPIDER_4561(2344, 9879, 0, SOUTH, 5)
        SPIDER_4561(2344, 9905, 0, SOUTH, 5)
        SPIDER_4561(2345, 9888, 0, SOUTH, 5)
        3104(2346, 9856, 0, SOUTH, 2)
        3104(2346, 9864, 0, SOUTH, 2)
        SPIDER_4561(2346, 9873, 0, SOUTH, 5)
        SPIDER_4561(2346, 9881, 0, SOUTH, 5)
        SPIDER_4561(2346, 9890, 0, SOUTH, 5)
        SPIDER_4561(2346, 9915, 0, SOUTH, 5)
        3104(2347, 9856, 0, SOUTH, 2)
        SPIDER_4561(2347, 9878, 0, SOUTH, 5)
        SPIDER_4561(2347, 9887, 0, SOUTH, 5)
        SPIDER_4561(2347, 9900, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2347, 9916, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2348, 9914, 0, SOUTH, 5)
        BLESSED_SPIDER(2349, 9894, 0, SOUTH, 5)
        SPIDER_4561(2349, 9897, 0, SOUTH, 5)
        BLESSED_SPIDER(2349, 9899, 0, SOUTH, 5)
        SPIDER_4561(2349, 9916, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2349, 9918, 0, SOUTH, 5)
        3104(2350, 9867, 0, SOUTH, 2)
        SPIDER_4561(2350, 9875, 0, SOUTH, 5)
        BLESSED_SPIDER(2350, 9896, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2350, 9911, 0, SOUTH, 5)
        BLESSED_SPIDER(2351, 9895, 0, SOUTH, 5)
        BLESSED_SPIDER(2351, 9896, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2351, 9910, 0, SOUTH, 5)
        3104(2352, 9856, 0, SOUTH, 2)
        BLESSED_SPIDER(2352, 9897, 0, SOUTH, 5)
        BLESSED_SPIDER(2352, 9899, 0, SOUTH, 5)
        SPIDER_4561(2352, 9909, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2352, 9914, 0, SOUTH, 5)
        BLESSED_SPIDER(2353, 9896, 0, SOUTH, 5)
        SPIDER_4561(2353, 9899, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2353, 9916, 0, SOUTH, 5)
        SPIDER_4561(2354, 9878, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2354, 9902, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2354, 9903, 0, SOUTH, 5)
        SPIDER_4561(2354, 9914, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2355, 9902, 0, SOUTH, 5)
        SPIDER_4561(2355, 9905, 0, SOUTH, 5)
        SPIDER_4561(2355, 9907, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2355, 9910, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2355, 9911, 0, SOUTH, 5)
        SPIDER_4561(2356, 9881, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2356, 9909, 0, SOUTH, 5)
        9216(2356, 9911, 0, SOUTH, 5)
        SPIDER_4561(2357, 9885, 0, SOUTH, 5)
        SPIDER_4561(2357, 9889, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2357, 9902, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2357, 9916, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2358, 9902, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2358, 9916, 0, SOUTH, 5)
        SPIDER_4561(2358, 9918, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2359, 9912, 0, SOUTH, 5)
        SPIDER_4561(2360, 9879, 0, SOUTH, 5)
        SPIDER_4561(2360, 9891, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2360, 9911, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2360, 9912, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2360, 9914, 0, SOUTH, 5)
        SPIDER_4561(2361, 9884, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2361, 9906, 0, SOUTH, 5)
        SPIDER_4561(2361, 9911, 0, SOUTH, 5)
        SPIDER_4561(2362, 9894, 0, SOUTH, 5)
        SPIDER_4561(2362, 9904, 0, SOUTH, 5)
        SPIDER_4561(2363, 9905, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2363, 9908, 0, SOUTH, 5)
        SPIDER_4561(2363, 9910, 0, SOUTH, 5)
        SPIDER_4561(2363, 9916, 0, SOUTH, 5)
        SPIDER_4561(2364, 9897, 0, SOUTH, 5)
        BLESSED_SPIDER_8978(2364, 9912, 0, SOUTH, 5)
        SPIDER_4561(2364, 9914, 0, SOUTH, 5)
    }
}