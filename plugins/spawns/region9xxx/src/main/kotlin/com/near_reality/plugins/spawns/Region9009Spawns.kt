package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9009Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY_235(2240, 3148, 0, SOUTH, 7)
        RABBIT_3420(2240, 3164, 0, SOUTH, 4)
        BUTTERFLY(2241, 3139, 0, SOUTH, 7)
        BUTTERFLY(2241, 3144, 0, SOUTH, 7)
        RABBIT_3421(2241, 3163, 0, SOUTH, 3)
        BUTTERFLY_235(2242, 3143, 0, SOUTH, 7)
        BUTTERFLY(2242, 3150, 0, SOUTH, 7)
        BUTTERFLY_235(2242, 3151, 0, <PERSON>OUT<PERSON>, 7)
        RABBIT_3422(2242, 3162, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        RABBIT_3421(2242, 3164, 0, SOUTH, 3)
        BUTTERFLY(2243, 3140, 0, SOUTH, 7)
        RABBIT_3420(2243, 3161, 0, SOUTH, 4)
        BUTTERFLY(2244, 3137, 0, SOUTH, 7)
        BUTTERFLY_235(2244, 3138, 0, SOUTH, 7)
        BUTTERFLY_235(2244, 3144, 0, SOUTH, 7)
        BUTTERFLY(2244, 3146, 0, SOUTH, 7)
        BUTTERFLY_235(2244, 3179, 0, SOUTH, 7)
        BUTTERFLY_235(2245, 3137, 0, SOUTH, 7)
        BUTTERFLY(2245, 3139, 0, SOUTH, 7)
        BUTTERFLY(2245, 3141, 0, SOUTH, 7)
        BUTTERFLY(2245, 3146, 0, SOUTH, 7)
        BUTTERFLY(2246, 3139, 0, SOUTH, 7)
        BUTTERFLY(2246, 3142, 0, SOUTH, 7)
        BUTTERFLY_235(2246, 3181, 0, SOUTH, 7)
        BUTTERFLY(2247, 3138, 0, SOUTH, 7)
        BUTTERFLY(2247, 3141, 0, SOUTH, 7)
        BUTTERFLY_235(2247, 3143, 0, SOUTH, 7)
        BUTTERFLY(2247, 3144, 0, SOUTH, 7)
        BUTTERFLY(2247, 3147, 0, SOUTH, 7)
        BUTTERFLY(2247, 3177, 0, SOUTH, 7)
        BUTTERFLY_235(2249, 3142, 0, SOUTH, 7)
        BUTTERFLY_235(2249, 3143, 0, SOUTH, 7)
        BUTTERFLY_235(2250, 3137, 0, SOUTH, 7)
        BUTTERFLY(2250, 3177, 0, SOUTH, 7)
        BUTTERFLY_235(2250, 3180, 0, SOUTH, 7)
        BUTTERFLY(2251, 3138, 0, SOUTH, 7)
        BUTTERFLY(2251, 3141, 0, SOUTH, 7)
        BUTTERFLY_235(2252, 3139, 0, SOUTH, 7)
        BUTTERFLY(2252, 3140, 0, SOUTH, 7)
        BUTTERFLY(2253, 3141, 0, SOUTH, 7)
        BUTTERFLY(2254, 3137, 0, SOUTH, 7)
        BUTTERFLY(2254, 3139, 0, SOUTH, 7)
        BUTTERFLY_235(2254, 3140, 0, SOUTH, 7)
        BUTTERFLY(2255, 3175, 0, SOUTH, 7)
        4556(2256, 3167, 0, SOUTH, 5)
        4259(2256, 3168, 0, SOUTH, 3)
        BUTTERFLY(2256, 3173, 0, SOUTH, 7)
        889(2257, 3149, 0, SOUTH, 3)
        BUTTERFLY(2257, 3177, 0, SOUTH, 7)
        RABBIT_3420(2264, 3140, 0, SOUTH, 4)
        RABBIT_3422(2265, 3141, 0, SOUTH, 3)
        RABBIT_3420(2273, 3182, 0, SOUTH, 4)
        RABBIT_3420(2274, 3164, 0, SOUTH, 4)
        BUTTERFLY_238(2275, 3161, 0, SOUTH, 0)
        BUTTERFLY_237(2277, 3183, 0, SOUTH, 7)
        BUTTERFLY_235(2278, 3141, 0, SOUTH, 7)
        RABBIT_3422(2278, 3160, 0, SOUTH, 3)
        BUTTERFLY_235(2279, 3137, 0, SOUTH, 7)
        RABBIT_3420(2279, 3158, 0, SOUTH, 4)
        9392(2279, 3188, 0, SOUTH, 0)
        BUTTERFLY_235(2281, 3139, 0, SOUTH, 7)
        WILL_O_THE_WISP(2281, 3142, 0, SOUTH, 0)
        WILL_O_THE_WISP_3441(2282, 3140, 0, SOUTH, 0)
        BUTTERFLY(2282, 3147, 0, SOUTH, 7)
        WILL_O_THE_WISP(2283, 3136, 0, SOUTH, 0)
        WILL_O_THE_WISP(2283, 3145, 0, SOUTH, 0)
        BUTTERFLY_235(2284, 3140, 0, SOUTH, 7)
        BUTTERFLY(2284, 3151, 0, SOUTH, 7)
        BUTTERFLY(2286, 3146, 0, SOUTH, 7)
        BUTTERFLY(2287, 3150, 0, SOUTH, 7)
        BUTTERFLY(2287, 3153, 0, SOUTH, 7)
        1116(2289, 3145, 0, SOUTH, 0)
        BUTTERFLY(2290, 3154, 0, SOUTH, 7)
        890(2291, 3147, 0, SOUTH, 5)
        GRIZZLY_BEAR_CUB(2291, 3156, 0, SOUTH, 8)
        BUTTERFLY_238(2291, 3179, 0, SOUTH, 0)
        GRIZZLY_BEAR_CUB_3425(2292, 3153, 0, SOUTH, 10)
        GRIZZLY_BEAR_3423(2293, 3156, 0, SOUTH, 11)
        RABBIT_3420(2293, 3175, 0, SOUTH, 4)
        DIRE_WOLF(2294, 3185, 0, SOUTH, 12)
        RABBIT_3420(2295, 3177, 0, SOUTH, 4)
        BUTTERFLY_238(2296, 3164, 0, SOUTH, 0)
        RABBIT_3422(2296, 3176, 0, SOUTH, 3)
        DIRE_WOLF(2296, 3187, 0, SOUTH, 12)
        WILL_O_THE_WISP(2296, 3195, 0, SOUTH, 0)
        RABBIT_3421(2297, 3175, 0, SOUTH, 3)
        RABBIT_3420(2297, 3176, 0, SOUTH, 4)
        4559(2303, 3197, 0, SOUTH, 5)
    }
}