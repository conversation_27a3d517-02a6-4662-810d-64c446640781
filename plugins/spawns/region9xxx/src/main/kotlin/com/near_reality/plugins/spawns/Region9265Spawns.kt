package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9265Spawns : NPCSpawnsScript() {
    init {
        SHEEP_2693(2319, 3191, 0, SOUTH, 3)
        RAM_1262(2322, 3189, 0, SOUTH, 5)
        SHEEP_2699(2322, 3192, 0, SOUTH, 2)
        8817(2323, 3160, 0, SOUTH, 5)
        DALLDAV(2324, 3164, 0, SOUTH, 3)
        ORONWEN(2324, 3179, 0, SOUTH, 2)
        SHEEP_2697(2325, 3190, 0, SOUTH, 4)
        3206(2326, 3149, 0, SOUTH, 0)
        8828(2326, 3161, 0, <PERSON>O<PERSON><PERSON>, 5)
        ELF_ARCHER(2329, 3170, 0, <PERSON>OUTH, 6)
        ELF_WARRIOR_5294(2329, 3173, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        MAWRTH(2331, 3165, 0, SOUTH, 4)
        EUDAV(2334, 3182, 0, SOUTH, 3)
        IONA(2335, 3169, 0, SOUTH, 3)
        ARVEL(2335, 3177, 0, SOUTH, 4)
        GOREU(2337, 3159, 0, SOUTH, 5)
        4547(2339, 3164, 0, SOUTH, 5)
        EOIN(2340, 3173, 0, SOUTH, 5)
        ELF_WARRIOR(2345, 3175, 0, SOUTH, 6)
        LILIWEN(2346, 3160, 0, SOUTH, 2)
        TOOL_LEPRECHAUN(2346, 3164, 0, SOUTH, 0)
        4542(2349, 3170, 0, SOUTH, 5)
        3427(2349, 3173, 0, SOUTH, 0)
        4260(2351, 3170, 0, SOUTH, 2)
        4258(2351, 3174, 0, SOUTH, 0)
        BANKER_1479(2352, 3166, 0, SOUTH, 0)
        4007(2352, 3170, 0, SOUTH, 2)
        4261(2352, 3174, 0, SOUTH, 4)
        6286(2353, 3171, 0, SOUTH, 0)
        5292(2353, 3172, 0, SOUTH, 0)
        4549(2353, 3173, 0, SOUTH, 5)
        4539(2353, 3175, 0, SOUTH, 5)
        4544(2353, 3179, 0, SOUTH, 5)
        8824(2354, 3155, 0, SOUTH, 5)
        BANKER_1480(2354, 3166, 0, SOUTH, 0)
        4546(2354, 3176, 0, SOUTH, 5)
        ELF_ARCHER_5296(2336, 3167, 1, SOUTH, 2)
        5298(2337, 3166, 1, SOUTH, 4)
        4540(2337, 3177, 1, SOUTH, 5)
        GETHIN(2341, 3158, 1, SOUTH, 2)
        9234(2344, 3172, 1, SOUTH, 5)
        4550(2354, 3174, 1, SOUTH, 5)
        YSGAWYN(2338, 3166, 1, SOUTH, 5)
    }
}