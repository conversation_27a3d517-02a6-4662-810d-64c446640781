package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9874Spawns : NPCSpawnsScript() {
    init {
        SKOGRE_879(2453, 9393, 2, SOUTH, 5)
        ZOGRE_867(2459, 9394, 2, SOUTH, 5)
        SKOGRE_879(2461, 9403, 2, SOUTH, 5)
        SKOGRE_879(2464, 9380, 2, SOUTH, 5)
        ZOGRE_868(2466, 9389, 2, SOUTH, 5)
        ZOGRE_869(2477, 9384, 2, SOUTH, 5)
        ZOGRE(2484, 9392, 2, SOUTH, 5)
        ZOGRE_870(2485, 9398, 2, SOUTH, 5)
    }
}