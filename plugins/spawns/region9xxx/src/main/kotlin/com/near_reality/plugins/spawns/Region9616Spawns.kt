package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9616Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_7118(2370, 9266, 1, SOUT<PERSON>, 47)
        MANIACAL_MONKEY_7118(2372, 9268, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2377, 9273, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2378, 9218, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2378, 9232, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2378, 9235, 1, SOUT<PERSON>, 0)
        MANIACAL_MONKEY_ARCHER(2378, 9251, 1, <PERSON><PERSON><PERSON><PERSON>, 0)
        MANI<PERSON>AL_<PERSON><PERSON>KEY_ARCHER(2380, 9245, 1, <PERSON><PERSON><PERSON><PERSON>, 0)
        <PERSON><PERSON><PERSON>AL_MONKEY_ARCHER(2380, 9257, 1, <PERSON><PERSON><PERSON><PERSON>, 0)
        <PERSON><PERSON><PERSON><PERSON>_M<PERSON><PERSON>Y_7118(2380, 9271, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2382, 9223, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2383, 9272, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2385, 9222, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2385, 9228, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2385, 9260, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2385, 9273, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2387, 9237, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2388, 9273, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2389, 9235, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2389, 9272, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2391, 9223, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2392, 9270, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2393, 9241, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2393, 9248, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2394, 9225, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2394, 9259, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2394, 9272, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2396, 9230, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2396, 9246, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2397, 9236, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2397, 9257, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2398, 9271, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2398, 9273, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2399, 9234, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2400, 9222, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2402, 9270, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2403, 9262, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2404, 9222, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2405, 9247, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2405, 9273, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2406, 9245, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2406, 9271, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2407, 9218, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2409, 9217, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2409, 9233, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2409, 9258, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2409, 9275, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2411, 9260, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2411, 9273, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2413, 9232, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2413, 9237, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2414, 9241, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2415, 9254, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2415, 9271, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2416, 9256, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2417, 9272, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2419, 9245, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2420, 9250, 1, SOUTH, 0)
    }
}