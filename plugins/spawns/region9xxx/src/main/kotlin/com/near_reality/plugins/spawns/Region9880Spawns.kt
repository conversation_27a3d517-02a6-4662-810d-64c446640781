package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9880Spawns : NPCSpawnsScript() {
    init {
        HELLHOUND_105(2433, 9771, 0, SOUTH, 3)
        HELLHOUND_105(2433, 9784, 0, SOUTH, 3)
        ABERRANT_SPECTRE_7(2441, 9775, 0, SOUTH, 4)
        ABERRANT_SPECTRE_7(2443, 9781, 0, SOUTH, 4)
        ABERRANT_SPECTRE_6(2443, 9785, 0, SOUTH, 4)
        ABERRANT_SPECTRE_6(2444, 9777, 0, SOUTH, 4)
        ABERRANT_SPECTRE_6(2456, 9777, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        ABERRANT_SPECTRE_7(2456, 9790, 0, <PERSON>O<PERSON><PERSON>, 4)
        ABERRANT_SPECTRE_7(2458, 9780, 0, SOUTH, 4)
        ABERRANT_SPECTRE_7(2459, 9774, 0, SOUTH, 4)
        ABERRANT_SPECTRE_7(2468, 9778, 0, SOUTH, 4)
        ABERRANT_SPECTRE_6(2470, 9782, 0, SOUTH, 4)
        ABERRANT_SPECTRE_6(2471, 9776, 0, SOUTH, 4)
    }
}