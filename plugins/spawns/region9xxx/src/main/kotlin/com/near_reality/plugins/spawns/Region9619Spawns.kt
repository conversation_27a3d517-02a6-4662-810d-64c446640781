package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9619Spawns : NPCSpawnsScript() {
    init {
        SMOKE_DEVIL(2368, 9442, 0, SOUTH, 4)
        SMOKE_DEVIL(2370, 9449, 0, SOUTH, 4)
        SMOKE_DEVIL(2380, 9429, 0, SOUTH, 4)
        SMOKE_DEVIL(2382, 9458, 0, SOUTH, 4)
        SMOKE_DEVIL(2382, 9465, 0, SOUTH, 4)
        SMOKE_DEVIL(2383, 9444, 0, SOUTH, 4)
        SMOKE_DEVIL(2384, 9434, 0, SOUTH, 4)
        SMOKE_DEVIL(2384, 9452, 0, SOUTH, 4)
        SMOKE_DEVIL(2390, 9459, 0, SOUTH, 4)
        SMOKE_DEVIL(2391, 9422, 0, <PERSON><PERSON>UTH, 4)
        SMOKE_DEVIL(2391, 9443, 0, <PERSON>OUTH, 4)
        SMOKE_DEVIL(2393, 9437, 0, SOUTH, 4)
        SMOKE_DEVIL(2395, 9450, 0, SOUTH, 4)
        SMOKE_DEVIL(2396, 9430, 0, SOUTH, 4)
        SMOKE_DEVIL(2398, 9442, 0, SOUTH, 4)
        SMOKE_DEVIL(2399, 9459, 0, SOUTH, 4)
        SMOKE_DEVIL(2401, 9421, 0, SOUTH, 4)
        SMOKE_DEVIL(2402, 9448, 0, SOUTH, 4)
        SMOKE_DEVIL(2404, 9454, 0, SOUTH, 4)
        SMOKE_DEVIL(2405, 9438, 0, SOUTH, 4)
        SMOKE_DEVIL(2406, 9433, 0, SOUTH, 4)
        SMOKE_DEVIL(2409, 9461, 0, SOUTH, 4)
        SMOKE_DEVIL(2410, 9452, 0, SOUTH, 4)
        SMOKE_DEVIL(2412, 9421, 0, SOUTH, 4)
        SMOKE_DEVIL(2412, 9427, 0, SOUTH, 4)
        SMOKE_DEVIL(2413, 9433, 0, SOUTH, 4)
        SMOKE_DEVIL(2413, 9444, 0, SOUTH, 4)
        SMOKE_DEVIL(2417, 9455, 0, SOUTH, 4)
        SMOKE_DEVIL(2419, 9424, 0, SOUTH, 4)
        SMOKE_DEVIL(2419, 9435, 0, SOUTH, 4)
        SMOKE_DEVIL(2421, 9460, 0, SOUTH, 4)
        SMOKE_DEVIL(2422, 9448, 0, SOUTH, 4)
    }
}