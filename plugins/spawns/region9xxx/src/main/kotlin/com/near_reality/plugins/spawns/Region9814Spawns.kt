package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9814Spawns : NPCSpawnsScript() {
    init {
        DWARF_5173(2436, 5526, 0, SOUTH, 4)
        DWARF_5171(2436, 5540, 0, SOUTH, 5)
        CAVE_GOBLIN_5166(2436, 5553, 0, SOUTH, 3)
        DWARF_5172(2437, 5549, 0, SOUTH, 5)
        TICKET_DWARF(2438, 5535, 0, SOUTH, 5)
        DWARF_5174(2439, 5520, 0, SOUTH, 5)
        CAVE_GOBLIN_5165(2439, 5525, 0, SO<PERSON><PERSON>, 3)
        CAVE_GOBLIN_5166(2439, 5532, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        CAVE_GOBLIN_5167(2439, 5539, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        DWARF_5170(2439, 5544, 0, <PERSON><PERSON>UTH, 5)
        CAVE_GOBLIN_5168(2439, 5549, 0, SOUTH, 2)
        CAVE_GOBLIN_5167(2483, 5539, 0, SOUTH, 3)
        CAVE_GOBLIN_5164(2484, 5522, 0, SOUTH, 2)
        DWARF_5172(2484, 5531, 0, SOUTH, 5)
        CAVE_GOBLIN_5168(2484, 5544, 0, SOUTH, 2)
        CAVE_GOBLIN_5167(2484, 5552, 0, SOUTH, 3)
        DWARF_5170(2485, 5535, 0, SOUTH, 5)
        TICKET_GOBLIN(2485, 5540, 0, SOUTH, 4)
        DWARF_5173(2486, 5525, 0, SOUTH, 4)
        CAVE_GOBLIN_5164(2486, 5548, 0, SOUTH, 2)
        CAVE_GOBLIN_5163(2487, 5519, 0, SOUTH, 3)
        CAVE_GOBLIN_5166(2487, 5530, 0, SOUTH, 3)
        DWARF_5175(2487, 5538, 0, SOUTH, 5)
        CAVE_GOBLIN_5165(2487, 5542, 0, SOUTH, 3)
    }
}