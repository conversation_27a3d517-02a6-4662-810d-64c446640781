package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9881Spawns : NPCSpawnsScript() {
    init {
        BLOODVELD_486(2434, 9817, 0, SOUTH, 2)
        BLOODVELD_486(2434, 9824, 0, SOUTH, 2)
        BLOODVELD_487(2440, 9821, 0, SOUTH, 6)
        BLOODVELD_486(2447, 9822, 0, SOUTH, 2)
        BLOODVELD_486(2451, 9817, 0, SOUTH, 2)
        ABERRANT_SPECTRE_6(2452, 9794, 0, SOUTH, 4)
        BLOODVELD_487(2453, 9822, 0, <PERSON>O<PERSON><PERSON>, 6)
        ABERRANT_SPECTRE_6(2460, 9793, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BLOODVELD_486(2465, 9830, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ANKOU_2519(2468, 9802, 0, <PERSON><PERSON>UT<PERSON>, 4)
        ANKOU_2518(2470, 9803, 0, SOUTH, 3)
        BLOODVELD_486(2470, 9834, 0, SOUTH, 2)
        ANKOU_2518(2472, 9797, 0, SOUTH, 3)
        BLOODVELD_487(2472, 9830, 0, SOUTH, 6)
        ANKOU_2517(2475, 9798, 0, SOUTH, 6)
        ANKOU_2519(2475, 9808, 0, SOUTH, 4)
        ANKOU_2517(2477, 9807, 0, SOUTH, 6)
        ANKOU_2518(2481, 9799, 0, SOUTH, 3)
        ANKOU_2517(2483, 9801, 0, SOUTH, 6)
        BLOODVELD_486(2485, 9822, 0, SOUTH, 2)
        BLOODVELD_487(2488, 9827, 0, SOUTH, 6)
        BLOODVELD_486(2489, 9818, 0, SOUTH, 2)
    }
}