package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9879Spawns : NPCSpawnsScript() {
    init {
        BLESSED_SPIDER(2432, 9724, 0, SOUTH, 5)
        BLESSED_SPIDER(2433, 9687, 0, SOUTH, 5)
        BLESSED_SPIDER(2434, 9693, 0, SOUTH, 5)
        BLESSED_SPIDER(2434, 9721, 0, SOUTH, 5)
        BLESSED_SPIDER(2435, 9690, 0, SOUTH, 5)
        BLESSED_SPIDER(2436, 9725, 0, SOUTH, 5)
        BLESSED_SPIDER(2437, 9688, 0, SOUTH, 5)
        BLESSED_SPIDER(2437, 9692, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        BLESSED_SPIDER(2437, 9722, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GOBLIN_3045(2438, 9700, 0, <PERSON><PERSON><PERSON><PERSON>, 22)
        GOBLIN_3045(2439, 9696, 0, <PERSON><PERSON><PERSON>H, 22)
        BLESSED_SPIDER(2440, 9689, 0, SOUTH, 5)
        GOBLIN_3045(2441, 9698, 0, SOUTH, 22)
        4529(2449, 9716, 0, SOUTH, 5)
        ZOMBIE(2452, 9684, 0, SOUTH, 5)
        ZOMBIE_28(2453, 9680, 0, SOUTH, 4)
        ZOMBIE_30(2455, 9677, 0, SOUTH, 5)
        ZOMBIE_49(2455, 9680, 0, SOUTH, 4)
        GIANT_BAT(2468, 9696, 0, SOUTH, 11)
        GIANT_BAT(2471, 9693, 0, SOUTH, 11)
        4530(2479, 9679, 0, SOUTH, 5)
        GIANT_BAT(2485, 9704, 0, SOUTH, 11)
        GIANT_BAT(2488, 9703, 0, SOUTH, 11)
        GIANT_BAT(2490, 9698, 0, SOUTH, 11)
        GIANT_BAT(2491, 9705, 0, SOUTH, 11)
    }
}