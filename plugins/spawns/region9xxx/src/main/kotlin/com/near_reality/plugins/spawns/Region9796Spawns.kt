package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region9796Spawns : NPCSpawnsScript() {
    init {
        BABY_BLACK_DRAGON(2438, 4393, 0, SOUTH, 3)
        BLACK_DRAGON_256(2448, 4359, 0, SOUTH, 3)
        BLACK_DRAGON_255(2454, 4370, 0, SOUTH, 3)
        BLACK_DRAGON_254(2459, 4359, 0, <PERSON>OUTH, 13)
        BLACK_DRAGON(2460, 4376, 0, SOUTH, 6)
    }
}